const Joi = require('joi');

// Common schemas
const objectIdSchema = Joi.string().regex(/^[0-9a-fA-F]{24}$/).message('Invalid ObjectId format');
const coordinatesSchema = Joi.array().items(Joi.number()).length(2);
const locationSchema = Joi.object({
  type: Joi.string().valid('Point').default('Point'),
  coordinates: coordinatesSchema.required()
});

// Bus validation schemas
const busValidation = {
  create: Joi.object({
    busNumber: Joi.string().required().trim().uppercase().max(20),
    registrationNumber: Joi.string().required().trim().uppercase().max(20),
    capacity: Joi.number().integer().min(1).max(100).required(),
    route: objectIdSchema.required(),
    driver: Joi.object({
      name: Joi.string().required().trim().max(100),
      licenseNumber: Joi.string().required().trim().max(50),
      phoneNumber: Joi.string().required().trim().max(20)
    }).required(),
    conductor: Joi.object({
      name: Joi.string().trim().max(100),
      employeeId: Joi.string().trim().max(50),
      phoneNumber: Joi.string().trim().max(20)
    }),
    features: Joi.object({
      hasWiFi: Joi.boolean().default(false),
      hasAC: Joi.boolean().default(false),
      isAccessible: Joi.boolean().default(false),
      hasCCTV: Joi.boolean().default(false),
      hasGPS: Joi.boolean().default(true)
    }),
    operationalHours: Joi.object({
      startTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).default('06:00'),
      endTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).default('22:00')
    }),
    emergencyContact: Joi.string().required().trim().max(20),
    nextMaintenanceDate: Joi.date().min('now').required()
  }),

  update: Joi.object({
    busNumber: Joi.string().trim().uppercase().max(20),
    registrationNumber: Joi.string().trim().uppercase().max(20),
    capacity: Joi.number().integer().min(1).max(100),
    route: objectIdSchema,
    driver: Joi.object({
      name: Joi.string().trim().max(100),
      licenseNumber: Joi.string().trim().max(50),
      phoneNumber: Joi.string().trim().max(20)
    }),
    conductor: Joi.object({
      name: Joi.string().trim().max(100),
      employeeId: Joi.string().trim().max(50),
      phoneNumber: Joi.string().trim().max(20)
    }),
    features: Joi.object({
      hasWiFi: Joi.boolean(),
      hasAC: Joi.boolean(),
      isAccessible: Joi.boolean(),
      hasCCTV: Joi.boolean(),
      hasGPS: Joi.boolean()
    }),
    operationalHours: Joi.object({
      startTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
      endTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    }),
    emergencyContact: Joi.string().trim().max(20),
    nextMaintenanceDate: Joi.date().min('now'),
    fuelLevel: Joi.number().min(0).max(100)
  }).min(1),

  updateLocation: Joi.object({
    longitude: Joi.number().min(-180).max(180).required(),
    latitude: Joi.number().min(-90).max(90).required(),
    accuracy: Joi.number().min(0).default(0),
    speed: Joi.number().min(0).max(200).default(0),
    heading: Joi.number().min(0).max(360).default(0)
  }),

  updatePassengers: Joi.object({
    count: Joi.number().integer().min(0).required()
  }),

  updateStatus: Joi.object({
    status: Joi.string().valid('active', 'inactive', 'maintenance', 'emergency').required()
  })
};

module.exports = { busValidation };
