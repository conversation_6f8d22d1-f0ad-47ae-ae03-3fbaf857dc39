const logger = require('../utils/logger');
const User = require('../models/User');
const Bus = require('../models/Bus');
const Route = require('../models/Route');

class NotificationService {
  constructor() {
    this.fcm = null;
    this.emailTransporter = null;
    this.smsClient = null;
  }

  /**
   * Initialize notification service
   */
  async initialize() {
    try {
      logger.info('Initializing Notification Service...');
      
      // Initialize Firebase Cloud Messaging
      if (process.env.FCM_SERVER_KEY) {
        await this.initializeFCM();
      }
      
      // Initialize Email service
      if (process.env.EMAIL_HOST && process.env.EMAIL_USER) {
        await this.initializeEmail();
      }
      
      // Initialize SMS service
      if (process.env.SMS_API_KEY) {
        await this.initializeSMS();
      }
      
      logger.info('Notification Service initialized successfully');
    } catch (error) {
      logger.error('Error initializing Notification Service:', error);
      throw error;
    }
  }

  /**
   * Initialize Firebase Cloud Messaging
   */
  async initializeFCM() {
    try {
      // This would typically use the Firebase Admin SDK
      // For now, we'll use a simple HTTP client approach
      this.fcm = {
        serverKey: process.env.FCM_SERVER_KEY,
        endpoint: 'https://fcm.googleapis.com/fcm/send'
      };
      
      logger.info('FCM initialized successfully');
    } catch (error) {
      logger.error('Error initializing FCM:', error);
    }
  }

  /**
   * Initialize Email service
   */
  async initializeEmail() {
    try {
      const nodemailer = require('nodemailer');
      
      this.emailTransporter = nodemailer.createTransporter({
        host: process.env.EMAIL_HOST,
        port: process.env.EMAIL_PORT || 587,
        secure: process.env.EMAIL_PORT == 465,
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS
        }
      });
      
      // Verify connection
      await this.emailTransporter.verify();
      logger.info('Email service initialized successfully');
    } catch (error) {
      logger.error('Error initializing email service:', error);
    }
  }

  /**
   * Initialize SMS service
   */
  async initializeSMS() {
    try {
      // This would integrate with SMS providers like Twilio, AWS SNS, etc.
      this.smsClient = {
        apiKey: process.env.SMS_API_KEY,
        apiSecret: process.env.SMS_API_SECRET,
        fromNumber: process.env.SMS_FROM_NUMBER
      };
      
      logger.info('SMS service initialized successfully');
    } catch (error) {
      logger.error('Error initializing SMS service:', error);
    }
  }

  /**
   * Send emergency alert to all relevant parties
   */
  async sendEmergencyAlert(alertData) {
    try {
      const { busId, type, message, severity, location, userId } = alertData;
      
      // Get bus and route information
      const bus = await Bus.findById(busId).populate('route');
      if (!bus) {
        throw new Error(`Bus not found: ${busId}`);
      }

      // Prepare alert message
      const alertMessage = {
        title: 'Emergency Alert',
        body: `${bus.busNumber}: ${message}`,
        data: {
          type: 'emergency',
          busId,
          busNumber: bus.busNumber,
          routeId: bus.route._id.toString(),
          routeName: bus.route.routeName,
          severity,
          location: JSON.stringify(location),
          timestamp: new Date().toISOString()
        }
      };

      // Send to different channels based on severity
      if (severity === 'critical') {
        await this.sendCriticalEmergencyAlert(alertMessage, bus);
      } else {
        await this.sendStandardEmergencyAlert(alertMessage, bus);
      }

      // Log the emergency alert
      logger.warn(`Emergency alert sent for bus ${bus.busNumber}:`, alertData);

      return { success: true, message: 'Emergency alert sent successfully' };
    } catch (error) {
      logger.error('Error sending emergency alert:', error);
      throw error;
    }
  }

  /**
   * Send critical emergency alert (highest priority)
   */
  async sendCriticalEmergencyAlert(alertMessage, bus) {
    try {
      // Send to emergency services
      await this.notifyEmergencyServices(alertMessage, bus);
      
      // Send to all admin users
      await this.notifyAdminUsers(alertMessage);
      
      // Send to route operators
      await this.notifyRouteOperators(alertMessage, bus.route._id);
      
      // Send SMS to emergency contacts
      await this.sendEmergencySMS(alertMessage, bus);
      
      // Send email to emergency contacts
      await this.sendEmergencyEmail(alertMessage, bus);
      
    } catch (error) {
      logger.error('Error sending critical emergency alert:', error);
    }
  }

  /**
   * Send standard emergency alert
   */
  async sendStandardEmergencyAlert(alertMessage, bus) {
    try {
      // Send to admin users
      await this.notifyAdminUsers(alertMessage);
      
      // Send to route operators
      await this.notifyRouteOperators(alertMessage, bus.route._id);
      
      // Send push notification to nearby passengers
      await this.notifyNearbyPassengers(alertMessage, bus);
      
    } catch (error) {
      logger.error('Error sending standard emergency alert:', error);
    }
  }

  /**
   * Send bus arrival notification
   */
  async sendBusArrivalNotification(busId, stopId, estimatedArrival) {
    try {
      const bus = await Bus.findById(busId).populate('route');
      const BusStop = require('../models/BusStop');
      const stop = await BusStop.findById(stopId);
      
      if (!bus || !stop) return;

      // Find users who have this stop as favorite or are subscribed to notifications
      const users = await User.find({
        'favoriteStops.stop': stopId,
        'preferences.notifications.busArrival': true,
        status: 'active'
      });

      const message = {
        title: 'Bus Arriving Soon',
        body: `Bus ${bus.busNumber} will arrive at ${stop.name} in ${Math.round((estimatedArrival - Date.now()) / 60000)} minutes`,
        data: {
          type: 'bus_arrival',
          busId: busId.toString(),
          stopId: stopId.toString(),
          estimatedArrival: estimatedArrival.toISOString()
        }
      };

      // Send push notifications
      await this.sendPushNotificationToUsers(users, message);

      logger.info(`Sent arrival notifications for bus ${bus.busNumber} at stop ${stop.name}`);
    } catch (error) {
      logger.error('Error sending bus arrival notification:', error);
    }
  }

  /**
   * Send route update notification
   */
  async sendRouteUpdateNotification(routeId, updateType, updateMessage) {
    try {
      const route = await Route.findById(routeId);
      if (!route) return;

      // Find users subscribed to this route
      const users = await User.find({
        'favoriteRoutes.route': routeId,
        'preferences.notifications.routeUpdates': true,
        status: 'active'
      });

      const message = {
        title: `Route Update - ${route.routeName}`,
        body: updateMessage,
        data: {
          type: 'route_update',
          routeId: routeId.toString(),
          updateType,
          timestamp: new Date().toISOString()
        }
      };

      await this.sendPushNotificationToUsers(users, message);

      logger.info(`Sent route update notifications for route ${route.routeName}`);
    } catch (error) {
      logger.error('Error sending route update notification:', error);
    }
  }

  /**
   * Send overcrowding alert
   */
  async sendOvercrowdingAlert(bus, passengerCount) {
    try {
      const message = {
        title: 'Bus Overcrowding Alert',
        body: `Bus ${bus.busNumber} is overcrowded (${passengerCount}/${bus.capacity} passengers)`,
        data: {
          type: 'overcrowding',
          busId: bus._id.toString(),
          passengerCount: passengerCount.toString(),
          capacity: bus.capacity.toString(),
          severity: 'medium'
        }
      };

      // Notify admin users and operators
      await this.notifyAdminUsers(message);
      await this.notifyRouteOperators(message, bus.route);

      logger.warn(`Overcrowding alert sent for bus ${bus.busNumber}`);
    } catch (error) {
      logger.error('Error sending overcrowding alert:', error);
    }
  }

  /**
   * Send maintenance reminder
   */
  async sendMaintenanceReminder(bus, daysUntilMaintenance) {
    try {
      const message = {
        title: 'Maintenance Reminder',
        body: `Bus ${bus.busNumber} requires maintenance in ${daysUntilMaintenance} days`,
        data: {
          type: 'maintenance_reminder',
          busId: bus._id.toString(),
          daysUntilMaintenance: daysUntilMaintenance.toString()
        }
      };

      // Send to maintenance team and operators
      await this.notifyMaintenanceTeam(message);
      await this.notifyAdminUsers(message);

      logger.info(`Maintenance reminder sent for bus ${bus.busNumber}`);
    } catch (error) {
      logger.error('Error sending maintenance reminder:', error);
    }
  }

  /**
   * Send push notification to specific users
   */
  async sendPushNotificationToUsers(users, message) {
    try {
      if (!this.fcm || !users.length) return;

      const tokens = [];
      users.forEach(user => {
        user.deviceTokens.forEach(deviceToken => {
          if (deviceToken.token) {
            tokens.push(deviceToken.token);
          }
        });
      });

      if (tokens.length === 0) return;

      // Send in batches of 500 (FCM limit)
      const batchSize = 500;
      for (let i = 0; i < tokens.length; i += batchSize) {
        const batch = tokens.slice(i, i + batchSize);
        await this.sendFCMNotification(batch, message);
      }

      logger.debug(`Sent push notifications to ${tokens.length} devices`);
    } catch (error) {
      logger.error('Error sending push notifications to users:', error);
    }
  }

  /**
   * Send FCM notification
   */
  async sendFCMNotification(tokens, message) {
    try {
      if (!this.fcm) return;

      const axios = require('axios');
      
      const payload = {
        registration_ids: tokens,
        notification: {
          title: message.title,
          body: message.body,
          sound: 'default',
          badge: 1
        },
        data: message.data,
        priority: 'high',
        time_to_live: 3600 // 1 hour
      };

      const response = await axios.post(this.fcm.endpoint, payload, {
        headers: {
          'Authorization': `key=${this.fcm.serverKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.failure > 0) {
        logger.warn(`FCM notification failures: ${response.data.failure}`);
      }

      return response.data;
    } catch (error) {
      logger.error('Error sending FCM notification:', error);
    }
  }

  /**
   * Notify admin users
   */
  async notifyAdminUsers(message) {
    try {
      const adminUsers = await User.find({
        role: { $in: ['admin', 'operator'] },
        status: 'active'
      });

      await this.sendPushNotificationToUsers(adminUsers, message);
    } catch (error) {
      logger.error('Error notifying admin users:', error);
    }
  }

  /**
   * Notify route operators
   */
  async notifyRouteOperators(message, routeId) {
    try {
      // This would typically find operators assigned to specific routes
      const operators = await User.find({
        role: 'operator',
        status: 'active'
        // Add route assignment logic here
      });

      await this.sendPushNotificationToUsers(operators, message);
    } catch (error) {
      logger.error('Error notifying route operators:', error);
    }
  }

  /**
   * Notify nearby passengers
   */
  async notifyNearbyPassengers(message, bus) {
    try {
      // Find users near the bus location
      const nearbyUsers = await User.find({
        status: 'active',
        'preferences.notifications.emergencyAlerts': true
        // Add location-based query here
      });

      await this.sendPushNotificationToUsers(nearbyUsers, message);
    } catch (error) {
      logger.error('Error notifying nearby passengers:', error);
    }
  }

  /**
   * Notify emergency services
   */
  async notifyEmergencyServices(message, bus) {
    try {
      // Send to emergency contacts defined in route
      if (bus.route.emergencyContacts) {
        for (const contact of bus.route.emergencyContacts) {
          await this.sendEmergencySMSToNumber(contact.phone, message.body);
          await this.sendEmergencyEmailToAddress(contact.email, message);
        }
      }

      logger.info(`Notified emergency services for bus ${bus.busNumber}`);
    } catch (error) {
      logger.error('Error notifying emergency services:', error);
    }
  }

  /**
   * Notify maintenance team
   */
  async notifyMaintenanceTeam(message) {
    try {
      const maintenanceUsers = await User.find({
        role: 'maintenance',
        status: 'active'
      });

      await this.sendPushNotificationToUsers(maintenanceUsers, message);
    } catch (error) {
      logger.error('Error notifying maintenance team:', error);
    }
  }

  /**
   * Send emergency SMS
   */
  async sendEmergencySMS(message, bus) {
    try {
      if (!this.smsClient) return;

      // Send to emergency contacts
      if (bus.emergencyContact) {
        await this.sendEmergencySMSToNumber(bus.emergencyContact, message.body);
      }
    } catch (error) {
      logger.error('Error sending emergency SMS:', error);
    }
  }

  /**
   * Send SMS to specific number
   */
  async sendEmergencySMSToNumber(phoneNumber, messageText) {
    try {
      if (!this.smsClient) return;

      // This would integrate with SMS provider API
      logger.info(`SMS sent to ${phoneNumber}: ${messageText}`);
      
      // Placeholder for actual SMS implementation
      // const response = await smsProvider.send({
      //   to: phoneNumber,
      //   from: this.smsClient.fromNumber,
      //   body: messageText
      // });
      
    } catch (error) {
      logger.error('Error sending SMS:', error);
    }
  }

  /**
   * Send emergency email
   */
  async sendEmergencyEmail(message, bus) {
    try {
      if (!this.emailTransporter) return;

      const emailContent = {
        from: process.env.EMAIL_FROM || process.env.EMAIL_USER,
        subject: message.title,
        html: `
          <h2>${message.title}</h2>
          <p><strong>Bus:</strong> ${bus.busNumber}</p>
          <p><strong>Route:</strong> ${bus.route.routeName}</p>
          <p><strong>Message:</strong> ${message.body}</p>
          <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
          <p><strong>Location:</strong> ${JSON.stringify(message.data.location)}</p>
        `
      };

      // Send to emergency contacts
      if (bus.route.emergencyContacts) {
        for (const contact of bus.route.emergencyContacts) {
          if (contact.email) {
            await this.sendEmergencyEmailToAddress(contact.email, emailContent);
          }
        }
      }
    } catch (error) {
      logger.error('Error sending emergency email:', error);
    }
  }

  /**
   * Send email to specific address
   */
  async sendEmergencyEmailToAddress(emailAddress, emailContent) {
    try {
      if (!this.emailTransporter) return;

      await this.emailTransporter.sendMail({
        ...emailContent,
        to: emailAddress
      });

      logger.info(`Emergency email sent to ${emailAddress}`);
    } catch (error) {
      logger.error('Error sending email:', error);
    }
  }

  /**
   * Get notification statistics
   */
  async getNotificationStats() {
    try {
      const stats = {
        totalUsers: await User.countDocuments({ status: 'active' }),
        usersWithNotificationsEnabled: await User.countDocuments({
          status: 'active',
          $or: [
            { 'preferences.notifications.busArrival': true },
            { 'preferences.notifications.routeUpdates': true },
            { 'preferences.notifications.emergencyAlerts': true }
          ]
        }),
        totalDeviceTokens: await User.aggregate([
          { $match: { status: 'active' } },
          { $project: { tokenCount: { $size: '$deviceTokens' } } },
          { $group: { _id: null, total: { $sum: '$tokenCount' } } }
        ])
      };

      return stats;
    } catch (error) {
      logger.error('Error getting notification stats:', error);
      return null;
    }
  }
}

module.exports = new NotificationService();
