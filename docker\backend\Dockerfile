# Use Node.js LTS version
FROM node:18-alpine AS base

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    curl \
    tzdata \
    && rm -rf /var/cache/apk/*

# Set timezone
ENV TZ=UTC

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Copy application code
COPY backend/ ./

# Create necessary directories
RUN mkdir -p logs uploads && \
    chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Start the application
CMD ["node", "src/server.js"]

# Development stage
FROM base AS development

# Switch back to root for development dependencies
USER root

# Install development dependencies
RUN npm ci && npm cache clean --force

# Install nodemon globally
RUN npm install -g nodemon

# Switch back to nodejs user
USER nodejs

# Start with nodemon for development
CMD ["nodemon", "src/server.js"]

# Production stage
FROM base AS production

# Production is the default, so we use the base stage as-is
# The CMD from base stage will be used
