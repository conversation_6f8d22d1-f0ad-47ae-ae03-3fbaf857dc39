const Bus = require('../models/Bus');
const GPSTracking = require('../models/GPSTracking');
const Route = require('../models/Route');
const BusStop = require('../models/BusStop');
const logger = require('../utils/logger');
const { AppError } = require('../utils/errors');
const { calculateDistance, calculateETA } = require('../utils/geoUtils');
const RealTimeService = require('../services/RealTimeService');

/**
 * Get all buses with filtering and pagination
 */
const getAllBuses = async (req, res, next) => {
  try {
    const {
      route,
      status,
      page = 1,
      limit = 10,
      sortBy = 'busNumber',
      sortOrder = 'asc'
    } = req.query;

    // Build filter object
    const filter = {};
    if (route) filter.route = route;
    if (status) filter.status = status;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    // Execute query with population
    const [buses, totalCount] = await Promise.all([
      Bus.find(filter)
        .populate('route', 'routeNumber routeName startPoint endPoint')
        .populate('currentStop', 'name location')
        .populate('nextStop', 'name location')
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit)),
      Bus.countDocuments(filter)
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / parseInt(limit));
    const hasNextPage = parseInt(page) < totalPages;
    const hasPrevPage = parseInt(page) > 1;

    res.status(200).json({
      success: true,
      data: {
        buses,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalCount,
          hasNextPage,
          hasPrevPage,
          limit: parseInt(limit)
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching buses:', error);
    next(new AppError('Failed to fetch buses', 500));
  }
};

/**
 * Get bus by ID with detailed information
 */
const getBusById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const bus = await Bus.findById(id)
      .populate('route')
      .populate('currentStop')
      .populate('nextStop');

    if (!bus) {
      return next(new AppError('Bus not found', 404));
    }

    // Get latest GPS tracking data
    const latestTracking = await GPSTracking.getLatestForBus(id);

    // Calculate additional information
    const busData = {
      ...bus.toObject(),
      latestTracking,
      locationAge: bus.locationAgeMinutes,
      occupancyPercentage: bus.occupancyPercentage,
      availableSeats: bus.availableSeats
    };

    res.status(200).json({
      success: true,
      data: busData
    });
  } catch (error) {
    logger.error('Error fetching bus:', error);
    next(new AppError('Failed to fetch bus details', 500));
  }
};

/**
 * Get current location of a bus
 */
const getBusLocation = async (req, res, next) => {
  try {
    const { id } = req.params;

    const bus = await Bus.findById(id).select('currentLocation busNumber status');
    
    if (!bus) {
      return next(new AppError('Bus not found', 404));
    }

    if (bus.status !== 'active') {
      return next(new AppError('Bus is not currently active', 400));
    }

    res.status(200).json({
      success: true,
      data: {
        busId: id,
        busNumber: bus.busNumber,
        location: bus.currentLocation,
        locationAge: bus.locationAgeMinutes
      }
    });
  } catch (error) {
    logger.error('Error fetching bus location:', error);
    next(new AppError('Failed to fetch bus location', 500));
  }
};

/**
 * Get passenger count for a bus
 */
const getPassengerCount = async (req, res, next) => {
  try {
    const { id } = req.params;

    const bus = await Bus.findById(id).select('currentPassengerCount capacity busNumber');
    
    if (!bus) {
      return next(new AppError('Bus not found', 404));
    }

    res.status(200).json({
      success: true,
      data: {
        busId: id,
        busNumber: bus.busNumber,
        currentPassengerCount: bus.currentPassengerCount,
        capacity: bus.capacity,
        availableSeats: bus.availableSeats,
        occupancyPercentage: bus.occupancyPercentage
      }
    });
  } catch (error) {
    logger.error('Error fetching passenger count:', error);
    next(new AppError('Failed to fetch passenger count', 500));
  }
};

/**
 * Find buses near a location
 */
const getNearbyBuses = async (req, res, next) => {
  try {
    const { longitude, latitude, radius = 1000 } = req.query;

    if (!longitude || !latitude) {
      return next(new AppError('Longitude and latitude are required', 400));
    }

    const buses = await Bus.findNearby(
      parseFloat(longitude),
      parseFloat(latitude),
      parseInt(radius)
    );

    // Enhance with additional data
    const enhancedBuses = await Promise.all(
      buses.map(async (bus) => {
        const distance = calculateDistance(
          parseFloat(latitude),
          parseFloat(longitude),
          bus.currentLocation.coordinates[1],
          bus.currentLocation.coordinates[0]
        );

        return {
          ...bus.toObject(),
          distanceFromUser: Math.round(distance),
          occupancyPercentage: bus.occupancyPercentage,
          availableSeats: bus.availableSeats
        };
      })
    );

    res.status(200).json({
      success: true,
      data: {
        buses: enhancedBuses,
        searchLocation: {
          longitude: parseFloat(longitude),
          latitude: parseFloat(latitude)
        },
        searchRadius: parseInt(radius)
      }
    });
  } catch (error) {
    logger.error('Error finding nearby buses:', error);
    next(new AppError('Failed to find nearby buses', 500));
  }
};

/**
 * Get all buses on a specific route
 */
const getBusesByRoute = async (req, res, next) => {
  try {
    const { routeId } = req.params;

    // Verify route exists
    const route = await Route.findById(routeId);
    if (!route) {
      return next(new AppError('Route not found', 404));
    }

    const buses = await Bus.findByRoute(routeId);

    // Enhance with ETA calculations
    const enhancedBuses = await Promise.all(
      buses.map(async (bus) => {
        let estimatedArrival = null;
        
        if (bus.nextStop && bus.currentLocation.coordinates[0] !== 0) {
          const nextStop = await BusStop.findById(bus.nextStop);
          if (nextStop) {
            estimatedArrival = calculateETA(
              bus.currentLocation.coordinates[1],
              bus.currentLocation.coordinates[0],
              nextStop.location.coordinates[1],
              nextStop.location.coordinates[0],
              bus.currentLocation.speed || 25 // Default speed if not available
            );
          }
        }

        return {
          ...bus.toObject(),
          estimatedArrival,
          occupancyPercentage: bus.occupancyPercentage,
          availableSeats: bus.availableSeats
        };
      })
    );

    res.status(200).json({
      success: true,
      data: {
        route: {
          id: route._id,
          routeNumber: route.routeNumber,
          routeName: route.routeName
        },
        buses: enhancedBuses
      }
    });
  } catch (error) {
    logger.error('Error fetching buses by route:', error);
    next(new AppError('Failed to fetch buses for route', 500));
  }
};

/**
 * Create a new bus
 */
const createBus = async (req, res, next) => {
  try {
    const busData = req.body;

    // Verify route exists
    const route = await Route.findById(busData.route);
    if (!route) {
      return next(new AppError('Route not found', 404));
    }

    const bus = new Bus(busData);
    await bus.save();

    await bus.populate('route');

    logger.info(`New bus created: ${bus.busNumber} by user ${req.user.id}`);

    res.status(201).json({
      success: true,
      message: 'Bus created successfully',
      data: bus
    });
  } catch (error) {
    if (error.code === 11000) {
      const field = Object.keys(error.keyValue)[0];
      return next(new AppError(`Bus with this ${field} already exists`, 400));
    }
    
    logger.error('Error creating bus:', error);
    next(new AppError('Failed to create bus', 500));
  }
};

/**
 * Update a bus
 */
const updateBus = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    // If route is being updated, verify it exists
    if (updateData.route) {
      const route = await Route.findById(updateData.route);
      if (!route) {
        return next(new AppError('Route not found', 404));
      }
    }

    const bus = await Bus.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('route');

    if (!bus) {
      return next(new AppError('Bus not found', 404));
    }

    logger.info(`Bus updated: ${bus.busNumber} by user ${req.user.id}`);

    res.status(200).json({
      success: true,
      message: 'Bus updated successfully',
      data: bus
    });
  } catch (error) {
    logger.error('Error updating bus:', error);
    next(new AppError('Failed to update bus', 500));
  }
};

/**
 * Update bus location
 */
const updateBusLocation = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { longitude, latitude, accuracy = 0, speed = 0, heading = 0 } = req.body;

    const bus = await Bus.findById(id);
    if (!bus) {
      return next(new AppError('Bus not found', 404));
    }

    // Update location
    await bus.updateLocation(longitude, latitude, accuracy, speed, heading);

    // Create GPS tracking record
    const trackingData = new GPSTracking({
      bus: id,
      location: {
        type: 'Point',
        coordinates: [longitude, latitude]
      },
      speed,
      heading,
      accuracy,
      route: bus.route,
      passengerCount: {
        current: bus.currentPassengerCount,
        capacity: bus.capacity
      }
    });

    await trackingData.save();

    // Broadcast real-time update
    RealTimeService.broadcastBusLocationUpdate(id, {
      location: bus.currentLocation,
      speed,
      heading,
      passengerCount: bus.currentPassengerCount,
      availableSeats: bus.availableSeats
    });

    res.status(200).json({
      success: true,
      message: 'Location updated successfully',
      data: {
        location: bus.currentLocation,
        locationAge: 0
      }
    });
  } catch (error) {
    logger.error('Error updating bus location:', error);
    next(new AppError('Failed to update bus location', 500));
  }
};

/**
 * Update passenger count
 */
const updatePassengerCount = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { count } = req.body;

    const bus = await Bus.findById(id);
    if (!bus) {
      return next(new AppError('Bus not found', 404));
    }

    await bus.updatePassengerCount(count);

    // Broadcast real-time update
    RealTimeService.broadcastPassengerCountUpdate(id, {
      currentPassengerCount: count,
      availableSeats: bus.capacity - count,
      occupancyPercentage: Math.round((count / bus.capacity) * 100)
    });

    logger.info(`Passenger count updated for bus ${bus.busNumber}: ${count}/${bus.capacity}`);

    res.status(200).json({
      success: true,
      message: 'Passenger count updated successfully',
      data: {
        currentPassengerCount: count,
        capacity: bus.capacity,
        availableSeats: bus.capacity - count,
        occupancyPercentage: Math.round((count / bus.capacity) * 100)
      }
    });
  } catch (error) {
    logger.error('Error updating passenger count:', error);
    next(new AppError(error.message || 'Failed to update passenger count', 500));
  }
};

/**
 * Update bus status
 */
const updateBusStatus = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const bus = await Bus.findByIdAndUpdate(
      id,
      { status },
      { new: true, runValidators: true }
    );

    if (!bus) {
      return next(new AppError('Bus not found', 404));
    }

    // Broadcast status update
    RealTimeService.broadcastBusStatusUpdate(id, { status });

    logger.info(`Bus status updated: ${bus.busNumber} -> ${status} by user ${req.user.id}`);

    res.status(200).json({
      success: true,
      message: 'Bus status updated successfully',
      data: {
        busId: id,
        busNumber: bus.busNumber,
        status
      }
    });
  } catch (error) {
    logger.error('Error updating bus status:', error);
    next(new AppError('Failed to update bus status', 500));
  }
};

/**
 * Delete a bus
 */
const deleteBus = async (req, res, next) => {
  try {
    const { id } = req.params;

    const bus = await Bus.findByIdAndDelete(id);
    if (!bus) {
      return next(new AppError('Bus not found', 404));
    }

    logger.info(`Bus deleted: ${bus.busNumber} by user ${req.user.id}`);

    res.status(200).json({
      success: true,
      message: 'Bus deleted successfully'
    });
  } catch (error) {
    logger.error('Error deleting bus:', error);
    next(new AppError('Failed to delete bus', 500));
  }
};

module.exports = {
  getAllBuses,
  getBusById,
  getBusLocation,
  getPassengerCount,
  getNearbyBuses,
  getBusesByRoute,
  createBus,
  updateBus,
  updateBusLocation,
  updatePassengerCount,
  updateBusStatus,
  deleteBus
};
