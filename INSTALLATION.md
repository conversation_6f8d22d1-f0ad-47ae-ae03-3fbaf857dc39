# Government Bus Tracking System - Installation Guide

This guide will help you set up the complete GPS-based bus tracking system for government buses.

## System Requirements

### Hardware Requirements
- **Server**: Minimum 4 CPU cores, 8GB RAM, 100GB storage
- **Database**: Minimum 2 CPU cores, 4GB RAM, 50GB storage
- **GPS Devices**: Compatible GPS trackers for each bus
- **Mobile Devices**: Android 6.0+ or iOS 12.0+ for mobile app

### Software Requirements
- **Node.js**: Version 16.0 or higher
- **MongoDB**: Version 5.0 or higher
- **Redis**: Version 6.0 or higher
- **Docker**: Version 20.0+ (for containerized deployment)
- **React Native CLI**: For mobile app development

## Quick Start with Docker

### 1. <PERSON>lone the Repository
```bash
git clone <repository-url>
cd bustracking
```

### 2. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

### 3. Required Environment Variables
```bash
# Database
MONGO_ROOT_PASSWORD=your_secure_password
REDIS_PASSWORD=your_redis_password

# JWT Security
JWT_SECRET=your_super_secret_jwt_key_here_minimum_32_characters
ENCRYPTION_KEY=your_32_character_encryption_key

# Google Maps API
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# SMS Configuration (optional)
SMS_API_KEY=your_sms_api_key
SMS_API_SECRET=your_sms_api_secret

# Push Notifications
FCM_SERVER_KEY=your_firebase_server_key
```

### 4. Start the System
```bash
# Build and start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f backend
```

### 5. Initialize Database
```bash
# Run database migrations and seed data
docker-compose exec backend npm run migrate
docker-compose exec backend npm run seed
```

## Manual Installation

### 1. Install Dependencies

#### Backend Setup
```bash
# Install Node.js dependencies
npm install

# Install MongoDB
# Ubuntu/Debian:
sudo apt-get install mongodb

# macOS:
brew install mongodb-community

# Install Redis
# Ubuntu/Debian:
sudo apt-get install redis-server

# macOS:
brew install redis
```

#### Mobile App Setup
```bash
# Navigate to mobile directory
cd mobile

# Install dependencies
npm install

# iOS setup (macOS only)
cd ios && pod install && cd ..

# Android setup
# Ensure Android SDK is installed and configured
```

#### Web Dashboard Setup
```bash
# Navigate to web dashboard directory
cd web-dashboard

# Install dependencies
npm install
```

### 2. Database Setup

#### MongoDB Configuration
```bash
# Start MongoDB service
sudo systemctl start mongodb

# Create database and user
mongo
> use bus_tracking
> db.createUser({
    user: "bus_admin",
    pwd: "secure_password",
    roles: ["readWrite", "dbAdmin"]
  })
```

#### Redis Configuration
```bash
# Start Redis service
sudo systemctl start redis

# Configure Redis password
redis-cli
> CONFIG SET requirepass "your_redis_password"
> CONFIG REWRITE
```

### 3. Start Services

#### Backend API
```bash
# Development mode
npm run dev

# Production mode
npm start
```

#### Web Dashboard
```bash
cd web-dashboard
npm start
```

#### Mobile App
```bash
cd mobile

# iOS
npx react-native run-ios

# Android
npx react-native run-android
```

## Configuration

### 1. Google Maps API Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Maps JavaScript API, Places API, and Directions API
4. Create API key and add to environment variables
5. Configure API key restrictions for security

### 2. Firebase Push Notifications
1. Create Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Add Android and iOS apps to project
3. Download configuration files:
   - `google-services.json` for Android
   - `GoogleService-Info.plist` for iOS
4. Get server key from Project Settings > Cloud Messaging
5. Add server key to environment variables

### 3. Email Service Setup
1. Configure SMTP settings for your email provider
2. For Gmail, enable 2-factor authentication and create app password
3. Update environment variables with email credentials

### 4. SMS Service Setup (Optional)
1. Sign up with SMS provider (Twilio, AWS SNS, etc.)
2. Get API credentials
3. Update environment variables

## Database Schema

### Initial Data Setup
```bash
# Create admin user
curl -X POST http://localhost:3000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "firstName": "System",
    "lastName": "Administrator",
    "role": "admin"
  }'

# Create sample route
curl -X POST http://localhost:3000/api/v1/routes \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "routeNumber": "R001",
    "routeName": "City Center to Airport",
    "startPoint": {
      "name": "City Center",
      "location": {"coordinates": [-122.4194, 37.7749]},
      "address": "123 Main St, City Center"
    },
    "endPoint": {
      "name": "Airport Terminal",
      "location": {"coordinates": [-122.3748, 37.6213]},
      "address": "Airport Terminal, Gate A"
    }
  }'
```

## Security Configuration

### 1. SSL/TLS Setup
```bash
# Generate SSL certificates (for production)
sudo certbot --nginx -d yourdomain.com

# Or use self-signed certificates for development
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout docker/ssl/private.key \
  -out docker/ssl/certificate.crt
```

### 2. Firewall Configuration
```bash
# Allow necessary ports
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw allow 3000/tcp  # API (if not behind proxy)
sudo ufw enable
```

### 3. Database Security
```bash
# MongoDB security
# Edit /etc/mongod.conf
security:
  authorization: enabled
net:
  bindIp: 127.0.0.1

# Redis security
# Edit /etc/redis/redis.conf
requirepass your_strong_password
bind 127.0.0.1
```

## Monitoring and Logging

### 1. Application Monitoring
- Prometheus metrics available at `http://localhost:9090`
- Grafana dashboards at `http://localhost:3002`
- Default credentials: admin/admin123

### 2. Log Management
- ElasticSearch at `http://localhost:9200`
- Kibana at `http://localhost:5601`
- Application logs in `/logs` directory

### 3. Health Checks
```bash
# API health check
curl http://localhost:3000/health

# Database connectivity
curl http://localhost:3000/api/v1/health/db

# Redis connectivity
curl http://localhost:3000/api/v1/health/redis
```

## Backup and Recovery

### 1. Database Backup
```bash
# MongoDB backup
mongodump --host localhost --port 27017 --db bus_tracking --out /backup/

# Automated backup (runs daily at 2 AM)
docker-compose exec backup /app/backup.sh
```

### 2. File Backup
```bash
# Backup uploaded files and logs
tar -czf backup_$(date +%Y%m%d).tar.gz uploads/ logs/
```

## Troubleshooting

### Common Issues

#### 1. MongoDB Connection Error
```bash
# Check MongoDB status
sudo systemctl status mongodb

# Check MongoDB logs
sudo tail -f /var/log/mongodb/mongod.log

# Restart MongoDB
sudo systemctl restart mongodb
```

#### 2. Redis Connection Error
```bash
# Check Redis status
sudo systemctl status redis

# Test Redis connection
redis-cli ping

# Restart Redis
sudo systemctl restart redis
```

#### 3. Mobile App Build Issues
```bash
# Clear React Native cache
npx react-native start --reset-cache

# Clean Android build
cd android && ./gradlew clean && cd ..

# Clean iOS build (macOS only)
cd ios && xcodebuild clean && cd ..
```

#### 4. API Connection Issues
```bash
# Check API logs
docker-compose logs backend

# Check network connectivity
curl -I http://localhost:3000/health

# Restart API service
docker-compose restart backend
```

### Performance Optimization

#### 1. Database Optimization
```javascript
// Create indexes for better performance
db.buses.createIndex({ "currentLocation": "2dsphere" })
db.gps_trackings.createIndex({ "bus": 1, "timestamp": -1 })
db.routes.createIndex({ "routePath": "2dsphere" })
```

#### 2. Redis Caching
```bash
# Monitor Redis memory usage
redis-cli info memory

# Configure Redis memory limit
redis-cli config set maxmemory 256mb
redis-cli config set maxmemory-policy allkeys-lru
```

## Support and Maintenance

### Regular Maintenance Tasks
1. **Daily**: Check system health and logs
2. **Weekly**: Review performance metrics and alerts
3. **Monthly**: Update dependencies and security patches
4. **Quarterly**: Database optimization and cleanup

### Getting Help
- Check the troubleshooting section above
- Review application logs for error details
- Contact the development team with specific error messages
- Submit issues through the project repository

### System Updates
```bash
# Update application
git pull origin main
docker-compose build --no-cache
docker-compose up -d

# Update dependencies
npm update
docker-compose exec backend npm update
```

This installation guide provides a comprehensive setup process for the Government Bus Tracking System. Follow the steps carefully and ensure all security measures are properly configured for production deployment.
