const mongoose = require('mongoose');

const busSchema = new mongoose.Schema({
  busNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true
  },
  registrationNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true
  },
  capacity: {
    type: Number,
    required: true,
    min: 1,
    max: 100
  },
  currentPassengerCount: {
    type: Number,
    default: 0,
    min: 0
  },
  route: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Route',
    required: true
  },
  driver: {
    name: {
      type: String,
      required: true,
      trim: true
    },
    licenseNumber: {
      type: String,
      required: true,
      trim: true
    },
    phoneNumber: {
      type: String,
      required: true,
      trim: true
    }
  },
  conductor: {
    name: {
      type: String,
      trim: true
    },
    employeeId: {
      type: String,
      trim: true
    },
    phoneNumber: {
      type: String,
      trim: true
    }
  },
  currentLocation: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      default: [0, 0]
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    },
    accuracy: {
      type: Number,
      default: 0
    },
    speed: {
      type: Number,
      default: 0
    },
    heading: {
      type: Number,
      default: 0
    }
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'maintenance', 'emergency'],
    default: 'inactive'
  },
  isOnRoute: {
    type: Boolean,
    default: false
  },
  currentStop: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'BusStop',
    default: null
  },
  nextStop: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'BusStop',
    default: null
  },
  estimatedArrival: {
    type: Date,
    default: null
  },
  lastMaintenanceDate: {
    type: Date,
    default: Date.now
  },
  nextMaintenanceDate: {
    type: Date,
    required: true
  },
  fuelLevel: {
    type: Number,
    min: 0,
    max: 100,
    default: 100
  },
  emergencyContact: {
    type: String,
    required: true,
    trim: true
  },
  features: {
    hasWiFi: {
      type: Boolean,
      default: false
    },
    hasAC: {
      type: Boolean,
      default: false
    },
    isAccessible: {
      type: Boolean,
      default: false
    },
    hasCCTV: {
      type: Boolean,
      default: false
    },
    hasGPS: {
      type: Boolean,
      default: true
    }
  },
  operationalHours: {
    startTime: {
      type: String,
      required: true,
      default: '06:00'
    },
    endTime: {
      type: String,
      required: true,
      default: '22:00'
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Create geospatial index for location queries
busSchema.index({ 'currentLocation': '2dsphere' });

// Create compound indexes for efficient queries
busSchema.index({ route: 1, status: 1 });
busSchema.index({ busNumber: 1, status: 1 });
busSchema.index({ 'currentLocation.lastUpdated': -1 });

// Virtual for available seats
busSchema.virtual('availableSeats').get(function() {
  return this.capacity - this.currentPassengerCount;
});

// Virtual for occupancy percentage
busSchema.virtual('occupancyPercentage').get(function() {
  return Math.round((this.currentPassengerCount / this.capacity) * 100);
});

// Virtual for location age in minutes
busSchema.virtual('locationAgeMinutes').get(function() {
  if (!this.currentLocation.lastUpdated) return null;
  return Math.round((Date.now() - this.currentLocation.lastUpdated.getTime()) / (1000 * 60));
});

// Pre-save middleware to update timestamps
busSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Method to update location
busSchema.methods.updateLocation = function(longitude, latitude, accuracy = 0, speed = 0, heading = 0) {
  this.currentLocation = {
    type: 'Point',
    coordinates: [longitude, latitude],
    lastUpdated: new Date(),
    accuracy,
    speed,
    heading
  };
  return this.save();
};

// Method to update passenger count
busSchema.methods.updatePassengerCount = function(count) {
  if (count < 0 || count > this.capacity) {
    throw new Error('Invalid passenger count');
  }
  this.currentPassengerCount = count;
  return this.save();
};

// Static method to find buses near a location
busSchema.statics.findNearby = function(longitude, latitude, maxDistance = 1000) {
  return this.find({
    currentLocation: {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        $maxDistance: maxDistance
      }
    },
    status: 'active'
  });
};

// Static method to find buses on a specific route
busSchema.statics.findByRoute = function(routeId) {
  return this.find({ route: routeId, status: 'active' })
    .populate('route')
    .populate('currentStop')
    .populate('nextStop');
};

module.exports = mongoose.model('Bus', busSchema);
