const express = require('express');
const router = express.Router();
const busController = require('../controllers/busController');
const auth = require('../middleware/auth');
const validate = require('../middleware/validate');
const { busValidation } = require('../validations/busValidation');

/**
 * @swagger
 * components:
 *   schemas:
 *     Bus:
 *       type: object
 *       required:
 *         - busNumber
 *         - registrationNumber
 *         - capacity
 *         - route
 *       properties:
 *         busNumber:
 *           type: string
 *           description: Unique bus number
 *         registrationNumber:
 *           type: string
 *           description: Vehicle registration number
 *         capacity:
 *           type: number
 *           description: Maximum passenger capacity
 *         currentPassengerCount:
 *           type: number
 *           description: Current number of passengers
 *         route:
 *           type: string
 *           description: Route ID this bus operates on
 *         status:
 *           type: string
 *           enum: [active, inactive, maintenance, emergency]
 *           description: Current bus status
 */

/**
 * @swagger
 * /buses:
 *   get:
 *     summary: Get all buses
 *     tags: [Buses]
 *     parameters:
 *       - in: query
 *         name: route
 *         schema:
 *           type: string
 *         description: Filter by route ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive, maintenance, emergency]
 *         description: Filter by bus status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of buses per page
 *     responses:
 *       200:
 *         description: List of buses
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 buses:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Bus'
 *                 pagination:
 *                   type: object
 */
router.get('/', busController.getAllBuses);

/**
 * @swagger
 * /buses/{id}:
 *   get:
 *     summary: Get bus by ID
 *     tags: [Buses]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Bus ID
 *     responses:
 *       200:
 *         description: Bus details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Bus'
 *       404:
 *         description: Bus not found
 */
router.get('/:id', busController.getBusById);

/**
 * @swagger
 * /buses/{id}/location:
 *   get:
 *     summary: Get current location of a bus
 *     tags: [Buses]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Bus ID
 *     responses:
 *       200:
 *         description: Current bus location
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 location:
 *                   type: object
 *                   properties:
 *                     coordinates:
 *                       type: array
 *                       items:
 *                         type: number
 *                     lastUpdated:
 *                       type: string
 *                       format: date-time
 */
router.get('/:id/location', busController.getBusLocation);

/**
 * @swagger
 * /buses/{id}/passengers:
 *   get:
 *     summary: Get passenger count for a bus
 *     tags: [Buses]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Bus ID
 *     responses:
 *       200:
 *         description: Passenger information
 */
router.get('/:id/passengers', busController.getPassengerCount);

/**
 * @swagger
 * /buses/nearby:
 *   get:
 *     summary: Find buses near a location
 *     tags: [Buses]
 *     parameters:
 *       - in: query
 *         name: longitude
 *         required: true
 *         schema:
 *           type: number
 *         description: Longitude coordinate
 *       - in: query
 *         name: latitude
 *         required: true
 *         schema:
 *           type: number
 *         description: Latitude coordinate
 *       - in: query
 *         name: radius
 *         schema:
 *           type: number
 *           default: 1000
 *         description: Search radius in meters
 *     responses:
 *       200:
 *         description: List of nearby buses
 */
router.get('/nearby', busController.getNearbyBuses);

/**
 * @swagger
 * /buses/route/{routeId}:
 *   get:
 *     summary: Get all buses on a specific route
 *     tags: [Buses]
 *     parameters:
 *       - in: path
 *         name: routeId
 *         required: true
 *         schema:
 *           type: string
 *         description: Route ID
 *     responses:
 *       200:
 *         description: List of buses on the route
 */
router.get('/route/:routeId', busController.getBusesByRoute);

// Protected routes (require authentication)

/**
 * @swagger
 * /buses:
 *   post:
 *     summary: Create a new bus
 *     tags: [Buses]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Bus'
 *     responses:
 *       201:
 *         description: Bus created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 */
router.post('/', auth(['admin', 'operator']), validate(busValidation.create), busController.createBus);

/**
 * @swagger
 * /buses/{id}:
 *   put:
 *     summary: Update a bus
 *     tags: [Buses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Bus ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Bus'
 *     responses:
 *       200:
 *         description: Bus updated successfully
 *       404:
 *         description: Bus not found
 */
router.put('/:id', auth(['admin', 'operator']), validate(busValidation.update), busController.updateBus);

/**
 * @swagger
 * /buses/{id}/location:
 *   put:
 *     summary: Update bus location
 *     tags: [Buses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Bus ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - longitude
 *               - latitude
 *             properties:
 *               longitude:
 *                 type: number
 *               latitude:
 *                 type: number
 *               accuracy:
 *                 type: number
 *               speed:
 *                 type: number
 *               heading:
 *                 type: number
 *     responses:
 *       200:
 *         description: Location updated successfully
 */
router.put('/:id/location', auth(['driver', 'conductor', 'admin']), validate(busValidation.updateLocation), busController.updateBusLocation);

/**
 * @swagger
 * /buses/{id}/passengers:
 *   put:
 *     summary: Update passenger count
 *     tags: [Buses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Bus ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - count
 *             properties:
 *               count:
 *                 type: number
 *                 minimum: 0
 *     responses:
 *       200:
 *         description: Passenger count updated successfully
 */
router.put('/:id/passengers', auth(['driver', 'conductor', 'admin']), validate(busValidation.updatePassengers), busController.updatePassengerCount);

/**
 * @swagger
 * /buses/{id}/status:
 *   put:
 *     summary: Update bus status
 *     tags: [Buses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Bus ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [active, inactive, maintenance, emergency]
 *     responses:
 *       200:
 *         description: Status updated successfully
 */
router.put('/:id/status', auth(['driver', 'conductor', 'admin', 'operator']), validate(busValidation.updateStatus), busController.updateBusStatus);

/**
 * @swagger
 * /buses/{id}:
 *   delete:
 *     summary: Delete a bus
 *     tags: [Buses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Bus ID
 *     responses:
 *       200:
 *         description: Bus deleted successfully
 *       404:
 *         description: Bus not found
 */
router.delete('/:id', auth(['admin']), busController.deleteBus);

module.exports = router;
