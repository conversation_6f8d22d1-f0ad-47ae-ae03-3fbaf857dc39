const jwt = require('jsonwebtoken');
const User = require('../models/User');
const { AppError } = require('../utils/errors');
const logger = require('../utils/logger');

/**
 * Authentication middleware
 */
const auth = (allowedRoles = []) => {
  return async (req, res, next) => {
    try {
      // Get token from header
      const authHeader = req.header('Authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return next(new AppError('Access denied. No token provided.', 401));
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix

      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // Get user from database
      const user = await User.findById(decoded.id).select('-password');
      if (!user) {
        return next(new AppError('Invalid token. User not found.', 401));
      }

      // Check if user account is active
      if (user.status !== 'active') {
        return next(new AppError('Account is not active.', 401));
      }

      // Check if user account is locked
      if (user.isLocked) {
        return next(new AppError('Account is temporarily locked.', 401));
      }

      // Check role permissions
      if (allowedRoles.length > 0 && !allowedRoles.includes(user.role)) {
        return next(new AppError('Access denied. Insufficient permissions.', 403));
      }

      // Add user to request object
      req.user = user;
      next();
    } catch (error) {
      if (error.name === 'JsonWebTokenError') {
        return next(new AppError('Invalid token.', 401));
      }
      if (error.name === 'TokenExpiredError') {
        return next(new AppError('Token expired.', 401));
      }
      
      logger.error('Authentication error:', error);
      next(new AppError('Authentication failed.', 401));
    }
  };
};

/**
 * Optional authentication middleware (doesn't fail if no token)
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.header('Authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.id).select('-password');
      
      if (user && user.status === 'active' && !user.isLocked) {
        req.user = user;
      }
    }
    next();
  } catch (error) {
    // Ignore authentication errors for optional auth
    next();
  }
};

module.exports = auth;
module.exports.optionalAuth = optionalAuth;
