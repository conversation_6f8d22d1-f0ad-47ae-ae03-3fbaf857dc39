const GPSTracking = require('../models/GPSTracking');
const Bus = require('../models/Bus');
const Route = require('../models/Route');
const BusStop = require('../models/BusStop');
const logger = require('../utils/logger');
const { calculateDistance, calculateETA, isPointNearLine } = require('../utils/geoUtils');
const NotificationService = require('./NotificationService');
const RealTimeService = require('./RealTimeService');

class GPSTrackingService {
  constructor() {
    this.trackingInterval = null;
    this.processingQueue = [];
    this.isProcessing = false;
  }

  /**
   * Initialize the GPS tracking service
   */
  async initialize() {
    try {
      logger.info('Initializing GPS Tracking Service...');
      
      // Start processing queue
      this.startProcessingQueue();
      
      // Start periodic cleanup of old tracking data
      this.startCleanupSchedule();
      
      // Start ETA calculation updates
      this.startETAUpdates();
      
      logger.info('GPS Tracking Service initialized successfully');
    } catch (error) {
      logger.error('Error initializing GPS Tracking Service:', error);
      throw error;
    }
  }

  /**
   * Process incoming GPS location update
   */
  async processLocationUpdate(data) {
    try {
      const { busId, longitude, latitude, speed = 0, heading = 0, accuracy = 0, timestamp } = data;

      // Validate input data
      if (!busId || !longitude || !latitude) {
        throw new Error('Missing required GPS data: busId, longitude, latitude');
      }

      // Validate coordinates
      if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
        throw new Error('Invalid GPS coordinates');
      }

      // Get bus information
      const bus = await Bus.findById(busId).populate('route');
      if (!bus) {
        throw new Error(`Bus not found: ${busId}`);
      }

      // Create tracking record
      const trackingData = {
        bus: busId,
        location: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        timestamp: timestamp ? new Date(timestamp) : new Date(),
        speed: Math.max(0, speed),
        heading: Math.max(0, Math.min(360, heading)),
        accuracy: Math.max(0, accuracy),
        route: bus.route._id,
        passengerCount: {
          current: bus.currentPassengerCount,
          capacity: bus.capacity
        }
      };

      // Add to processing queue
      this.processingQueue.push(trackingData);

      // Update bus current location immediately
      await bus.updateLocation(longitude, latitude, accuracy, speed, heading);

      // Broadcast real-time update
      RealTimeService.broadcastBusLocationUpdate(busId, {
        location: bus.currentLocation,
        speed,
        heading,
        passengerCount: bus.currentPassengerCount,
        availableSeats: bus.availableSeats,
        timestamp: trackingData.timestamp
      });

      return { success: true, message: 'Location update processed' };
    } catch (error) {
      logger.error('Error processing location update:', error);
      throw error;
    }
  }

  /**
   * Update passenger count for a bus
   */
  async updatePassengerCount(busId, count) {
    try {
      const bus = await Bus.findById(busId);
      if (!bus) {
        throw new Error(`Bus not found: ${busId}`);
      }

      await bus.updatePassengerCount(count);

      // Create tracking record for passenger count update
      const latestTracking = await GPSTracking.getLatestForBus(busId);
      if (latestTracking) {
        const trackingData = new GPSTracking({
          bus: busId,
          location: latestTracking.location,
          timestamp: new Date(),
          speed: latestTracking.speed,
          heading: latestTracking.heading,
          accuracy: latestTracking.accuracy,
          route: bus.route,
          passengerCount: {
            current: count,
            capacity: bus.capacity,
            lastUpdated: new Date()
          }
        });

        await trackingData.save();
      }

      // Broadcast passenger count update
      RealTimeService.broadcastPassengerCountUpdate(busId, {
        currentPassengerCount: count,
        availableSeats: bus.capacity - count,
        occupancyPercentage: Math.round((count / bus.capacity) * 100)
      });

      // Check for overcrowding alert
      if (count > bus.capacity) {
        await NotificationService.sendOvercrowdingAlert(bus, count);
      }

      return { success: true, message: 'Passenger count updated' };
    } catch (error) {
      logger.error('Error updating passenger count:', error);
      throw error;
    }
  }

  /**
   * Start processing queue for GPS data
   */
  startProcessingQueue() {
    setInterval(async () => {
      if (this.processingQueue.length > 0 && !this.isProcessing) {
        this.isProcessing = true;
        
        try {
          const batch = this.processingQueue.splice(0, 10); // Process 10 records at a time
          await this.processBatch(batch);
        } catch (error) {
          logger.error('Error processing GPS batch:', error);
        } finally {
          this.isProcessing = false;
        }
      }
    }, 1000); // Process every second
  }

  /**
   * Process a batch of GPS tracking data
   */
  async processBatch(batch) {
    try {
      const trackingRecords = [];

      for (const data of batch) {
        // Enhanced processing for each record
        const enhancedData = await this.enhanceTrackingData(data);
        trackingRecords.push(enhancedData);
      }

      // Bulk insert tracking records
      if (trackingRecords.length > 0) {
        await GPSTracking.insertMany(trackingRecords);
        logger.debug(`Processed ${trackingRecords.length} GPS tracking records`);
      }
    } catch (error) {
      logger.error('Error processing GPS batch:', error);
    }
  }

  /**
   * Enhance tracking data with additional calculations
   */
  async enhanceTrackingData(data) {
    try {
      // Find nearest bus stop
      const nearestStops = await BusStop.aggregate([
        {
          $geoNear: {
            near: data.location,
            distanceField: 'distance',
            maxDistance: 500, // 500 meters
            spherical: true,
            query: { status: 'active' }
          }
        },
        { $limit: 1 }
      ]);

      if (nearestStops.length > 0) {
        data.nearestStop = {
          stop: nearestStops[0]._id,
          distance: Math.round(nearestStops[0].distance)
        };
      }

      // Calculate route progress
      const route = await Route.findById(data.route);
      if (route) {
        const routeProgress = await this.calculateRouteProgress(data.location, route);
        data.routeProgress = routeProgress;

        // Check geofencing
        const isOnRoute = this.checkGeofencing(data.location, route);
        data.geofencing = {
          isOnRoute,
          deviationDistance: isOnRoute ? 0 : (data.nearestStop?.distance || 999999),
          lastOnRouteTime: isOnRoute ? new Date() : undefined
        };

        // Generate alerts
        data.alerts = await this.generateAlerts(data, route);
      }

      return data;
    } catch (error) {
      logger.error('Error enhancing tracking data:', error);
      return data;
    }
  }

  /**
   * Calculate route progress
   */
  async calculateRouteProgress(location, route) {
    try {
      const routePath = route.routePath.coordinates;
      const totalDistance = route.distance;

      // Find closest point on route
      let minDistance = Infinity;
      let closestSegmentIndex = 0;
      let progressDistance = 0;

      for (let i = 0; i < routePath.length - 1; i++) {
        const segmentStart = routePath[i];
        const segmentEnd = routePath[i + 1];
        
        const distance = this.distanceToLineSegment(
          location.coordinates,
          segmentStart,
          segmentEnd
        );

        if (distance < minDistance) {
          minDistance = distance;
          closestSegmentIndex = i;
        }
      }

      // Calculate distance covered up to closest segment
      for (let i = 0; i < closestSegmentIndex; i++) {
        progressDistance += calculateDistance(
          routePath[i][1], routePath[i][0],
          routePath[i + 1][1], routePath[i + 1][0]
        ) / 1000; // Convert to km
      }

      const percentageComplete = Math.min(100, (progressDistance / totalDistance) * 100);
      const remainingDistance = Math.max(0, totalDistance - progressDistance);
      const estimatedTimeToDestination = remainingDistance / (route.averageSpeed || 25) * 60; // minutes

      return {
        distanceCovered: Math.round(progressDistance * 100) / 100,
        percentageComplete: Math.round(percentageComplete),
        estimatedTimeToDestination: Math.round(estimatedTimeToDestination)
      };
    } catch (error) {
      logger.error('Error calculating route progress:', error);
      return {
        distanceCovered: 0,
        percentageComplete: 0,
        estimatedTimeToDestination: 0
      };
    }
  }

  /**
   * Check if bus is within route geofence
   */
  checkGeofencing(location, route) {
    try {
      const routePath = route.routePath.coordinates;
      const buffer = 200; // 200 meters buffer

      for (let i = 0; i < routePath.length - 1; i++) {
        const segmentStart = routePath[i];
        const segmentEnd = routePath[i + 1];
        
        if (isPointNearLine(location.coordinates, segmentStart, segmentEnd, buffer)) {
          return true;
        }
      }

      return false;
    } catch (error) {
      logger.error('Error checking geofencing:', error);
      return true; // Default to true to avoid false alarms
    }
  }

  /**
   * Generate alerts based on tracking data
   */
  async generateAlerts(data, route) {
    const alerts = [];

    try {
      // Speed violation alert
      const speedLimit = route.speedLimit || 60; // Default 60 km/h
      if (data.speed > speedLimit) {
        alerts.push({
          type: 'speed_violation',
          severity: data.speed > speedLimit * 1.3 ? 'critical' : 'high',
          message: `Speed violation: ${data.speed} km/h (limit: ${speedLimit} km/h)`,
          timestamp: new Date()
        });
      }

      // Route deviation alert
      if (!data.geofencing?.isOnRoute) {
        const deviationDistance = data.geofencing?.deviationDistance || 0;
        alerts.push({
          type: 'route_deviation',
          severity: deviationDistance > 500 ? 'high' : 'medium',
          message: `Bus has deviated ${deviationDistance}m from route`,
          timestamp: new Date()
        });
      }

      // Passenger overcrowding alert
      if (data.passengerCount.current > data.passengerCount.capacity) {
        alerts.push({
          type: 'passenger_limit',
          severity: 'high',
          message: `Passenger count exceeds capacity: ${data.passengerCount.current}/${data.passengerCount.capacity}`,
          timestamp: new Date()
        });
      }

      // Low accuracy alert
      if (data.accuracy > 50) { // More than 50 meters accuracy
        alerts.push({
          type: 'gps_accuracy',
          severity: 'low',
          message: `Low GPS accuracy: ${data.accuracy}m`,
          timestamp: new Date()
        });
      }

    } catch (error) {
      logger.error('Error generating alerts:', error);
    }

    return alerts;
  }

  /**
   * Calculate distance from point to line segment
   */
  distanceToLineSegment(point, lineStart, lineEnd) {
    const [px, py] = point;
    const [x1, y1] = lineStart;
    const [x2, y2] = lineEnd;

    const A = px - x1;
    const B = py - y1;
    const C = x2 - x1;
    const D = y2 - y1;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    let param = -1;

    if (lenSq !== 0) {
      param = dot / lenSq;
    }

    let xx, yy;

    if (param < 0) {
      xx = x1;
      yy = y1;
    } else if (param > 1) {
      xx = x2;
      yy = y2;
    } else {
      xx = x1 + param * C;
      yy = y1 + param * D;
    }

    return calculateDistance(py, px, yy, xx);
  }

  /**
   * Start cleanup schedule for old tracking data
   */
  startCleanupSchedule() {
    // Run cleanup every hour
    setInterval(async () => {
      try {
        const cutoffDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
        const result = await GPSTracking.deleteMany({ timestamp: { $lt: cutoffDate } });
        
        if (result.deletedCount > 0) {
          logger.info(`Cleaned up ${result.deletedCount} old GPS tracking records`);
        }
      } catch (error) {
        logger.error('Error during GPS tracking cleanup:', error);
      }
    }, 60 * 60 * 1000); // Every hour
  }

  /**
   * Start ETA updates for all active buses
   */
  startETAUpdates() {
    setInterval(async () => {
      try {
        await this.updateAllBusETAs();
      } catch (error) {
        logger.error('Error updating bus ETAs:', error);
      }
    }, 30000); // Every 30 seconds
  }

  /**
   * Update ETAs for all active buses
   */
  async updateAllBusETAs() {
    try {
      const activeBuses = await Bus.find({ status: 'active', isOnRoute: true })
        .populate('route')
        .populate('nextStop');

      for (const bus of activeBuses) {
        if (bus.nextStop && bus.currentLocation.coordinates[0] !== 0) {
          const eta = calculateETA(
            bus.currentLocation.coordinates[1],
            bus.currentLocation.coordinates[0],
            bus.nextStop.location.coordinates[1],
            bus.nextStop.location.coordinates[0],
            bus.currentLocation.speed || bus.route.averageSpeed || 25
          );

          bus.estimatedArrival = eta;
          await bus.save();

          // Broadcast ETA update
          RealTimeService.broadcastETAUpdate(bus._id, {
            nextStop: bus.nextStop._id,
            estimatedArrival: eta
          });
        }
      }
    } catch (error) {
      logger.error('Error updating bus ETAs:', error);
    }
  }

  /**
   * Get tracking statistics
   */
  async getTrackingStats() {
    try {
      const stats = await GPSTracking.aggregate([
        {
          $group: {
            _id: null,
            totalRecords: { $sum: 1 },
            averageAccuracy: { $avg: '$accuracy' },
            averageSpeed: { $avg: '$speed' },
            alertCount: { $sum: { $size: '$alerts' } }
          }
        }
      ]);

      return stats[0] || {
        totalRecords: 0,
        averageAccuracy: 0,
        averageSpeed: 0,
        alertCount: 0
      };
    } catch (error) {
      logger.error('Error getting tracking stats:', error);
      return null;
    }
  }
}

module.exports = new GPSTrackingService();
