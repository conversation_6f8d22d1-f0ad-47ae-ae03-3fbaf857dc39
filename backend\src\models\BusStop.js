const mongoose = require('mongoose');

const busStopSchema = new mongoose.Schema({
  stopId: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      required: true
    }
  },
  address: {
    street: {
      type: String,
      required: true,
      trim: true
    },
    city: {
      type: String,
      required: true,
      trim: true
    },
    state: {
      type: String,
      required: true,
      trim: true
    },
    zipCode: {
      type: String,
      trim: true
    },
    landmark: {
      type: String,
      trim: true
    }
  },
  routes: [{
    route: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Route',
      required: true
    },
    sequence: {
      type: Number,
      required: true,
      min: 1
    },
    distanceFromStart: {
      type: Number, // in kilometers
      required: true,
      min: 0
    },
    estimatedTimeFromStart: {
      type: Number, // in minutes
      required: true,
      min: 0
    }
  }],
  facilities: {
    shelter: {
      type: Boolean,
      default: false
    },
    seating: {
      type: Boolean,
      default: false
    },
    lighting: {
      type: Boolean,
      default: false
    },
    digitalDisplay: {
      type: Boolean,
      default: false
    },
    ticketMachine: {
      type: Boolean,
      default: false
    },
    restroom: {
      type: Boolean,
      default: false
    },
    parking: {
      type: Boolean,
      default: false
    },
    bikeRack: {
      type: Boolean,
      default: false
    }
  },
  accessibility: {
    wheelchairAccessible: {
      type: Boolean,
      default: false
    },
    tactilePaving: {
      type: Boolean,
      default: false
    },
    audioAnnouncements: {
      type: Boolean,
      default: false
    },
    brailleSignage: {
      type: Boolean,
      default: false
    }
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'maintenance', 'temporary'],
    default: 'active'
  },
  operatingHours: {
    start: {
      type: String,
      default: '05:00'
    },
    end: {
      type: String,
      default: '23:00'
    }
  },
  safetyFeatures: {
    cctv: {
      type: Boolean,
      default: false
    },
    emergencyButton: {
      type: Boolean,
      default: false
    },
    securityLighting: {
      type: Boolean,
      default: false
    },
    policePatrol: {
      type: Boolean,
      default: false
    }
  },
  nearbyPOIs: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    category: {
      type: String,
      enum: ['hospital', 'school', 'shopping', 'office', 'residential', 'tourist', 'government', 'transport'],
      required: true
    },
    distance: {
      type: Number, // in meters
      required: true,
      min: 0
    }
  }],
  crowdingData: {
    averageWaitingPassengers: {
      type: Number,
      default: 0,
      min: 0
    },
    peakHours: [{
      timeSlot: {
        type: String,
        required: true
      },
      averageCrowd: {
        type: Number,
        required: true,
        min: 0
      }
    }],
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },
  weatherProtection: {
    covered: {
      type: Boolean,
      default: false
    },
    windProtection: {
      type: Boolean,
      default: false
    },
    heatingCooling: {
      type: Boolean,
      default: false
    }
  },
  maintenanceSchedule: {
    lastCleaned: {
      type: Date,
      default: Date.now
    },
    lastInspected: {
      type: Date,
      default: Date.now
    },
    nextMaintenance: {
      type: Date,
      required: true
    }
  },
  emergencyContacts: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    phone: {
      type: String,
      required: true,
      trim: true
    },
    role: {
      type: String,
      required: true,
      enum: ['maintenance', 'security', 'medical', 'transport']
    }
  }],
  zone: {
    type: String,
    required: true,
    trim: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Create geospatial index
busStopSchema.index({ location: '2dsphere' });

// Create compound indexes
busStopSchema.index({ 'routes.route': 1, 'routes.sequence': 1 });
busStopSchema.index({ status: 1, zone: 1 });
busStopSchema.index({ stopId: 1, status: 1 });

// Virtual for total routes serving this stop
busStopSchema.virtual('totalRoutes').get(function() {
  return this.routes.length;
});

// Virtual for accessibility score
busStopSchema.virtual('accessibilityScore').get(function() {
  const features = this.accessibility;
  const total = Object.keys(features).length;
  const available = Object.values(features).filter(Boolean).length;
  return Math.round((available / total) * 100);
});

// Virtual for facility score
busStopSchema.virtual('facilityScore').get(function() {
  const features = this.facilities;
  const total = Object.keys(features).length;
  const available = Object.values(features).filter(Boolean).length;
  return Math.round((available / total) * 100);
});

// Pre-save middleware
busStopSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Method to get next buses for this stop
busStopSchema.methods.getNextBuses = async function(limit = 5) {
  const Bus = mongoose.model('Bus');
  const Route = mongoose.model('Route');
  
  const nextBuses = [];
  
  for (const routeInfo of this.routes) {
    const buses = await Bus.find({
      route: routeInfo.route,
      status: 'active',
      isOnRoute: true
    }).populate('route');
    
    for (const bus of buses) {
      // Calculate estimated arrival time based on current location and route
      // This is a simplified calculation - in reality, you'd use more sophisticated algorithms
      const estimatedArrival = new Date(Date.now() + Math.random() * 30 * 60000); // Random 0-30 minutes
      
      nextBuses.push({
        bus: bus,
        route: bus.route,
        estimatedArrival: estimatedArrival,
        currentPassengerCount: bus.currentPassengerCount,
        availableSeats: bus.availableSeats
      });
    }
  }
  
  return nextBuses
    .sort((a, b) => a.estimatedArrival - b.estimatedArrival)
    .slice(0, limit);
};

// Method to update crowding data
busStopSchema.methods.updateCrowdingData = function(waitingPassengers) {
  this.crowdingData.averageWaitingPassengers = waitingPassengers;
  this.crowdingData.lastUpdated = new Date();
  return this.save();
};

// Static method to find stops near a location
busStopSchema.statics.findNearby = function(longitude, latitude, maxDistance = 500) {
  return this.find({
    location: {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        $maxDistance: maxDistance
      }
    },
    status: 'active'
  }).populate('routes.route');
};

// Static method to find stops by route
busStopSchema.statics.findByRoute = function(routeId) {
  return this.find({
    'routes.route': routeId,
    status: 'active'
  }).sort({ 'routes.sequence': 1 });
};

// Static method to find stops in a zone
busStopSchema.statics.findByZone = function(zone) {
  return this.find({
    zone: zone,
    status: 'active'
  }).populate('routes.route');
};

module.exports = mongoose.model('BusStop', busStopSchema);
