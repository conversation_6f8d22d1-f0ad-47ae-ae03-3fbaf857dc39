const mongoose = require('mongoose');

const routeSchema = new mongoose.Schema({
  routeNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true
  },
  routeName: {
    type: String,
    required: true,
    trim: true
  },
  startPoint: {
    name: {
      type: String,
      required: true,
      trim: true
    },
    location: {
      type: {
        type: String,
        enum: ['Point'],
        default: 'Point'
      },
      coordinates: {
        type: [Number], // [longitude, latitude]
        required: true
      }
    },
    address: {
      type: String,
      required: true,
      trim: true
    }
  },
  endPoint: {
    name: {
      type: String,
      required: true,
      trim: true
    },
    location: {
      type: {
        type: String,
        enum: ['Point'],
        default: 'Point'
      },
      coordinates: {
        type: [Number], // [longitude, latitude]
        required: true
      }
    },
    address: {
      type: String,
      required: true,
      trim: true
    }
  },
  stops: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'BusStop'
  }],
  distance: {
    type: Number,
    required: true,
    min: 0
  },
  estimatedDuration: {
    type: Number, // in minutes
    required: true,
    min: 1
  },
  fare: {
    adult: {
      type: Number,
      required: true,
      min: 0
    },
    child: {
      type: Number,
      required: true,
      min: 0
    },
    senior: {
      type: Number,
      required: true,
      min: 0
    },
    student: {
      type: Number,
      required: true,
      min: 0
    }
  },
  schedule: {
    weekdays: {
      firstBus: {
        type: String,
        required: true,
        default: '06:00'
      },
      lastBus: {
        type: String,
        required: true,
        default: '22:00'
      },
      frequency: {
        type: Number, // minutes between buses
        required: true,
        min: 5,
        default: 15
      }
    },
    weekends: {
      firstBus: {
        type: String,
        required: true,
        default: '07:00'
      },
      lastBus: {
        type: String,
        required: true,
        default: '21:00'
      },
      frequency: {
        type: Number, // minutes between buses
        required: true,
        min: 5,
        default: 20
      }
    },
    holidays: {
      firstBus: {
        type: String,
        default: '08:00'
      },
      lastBus: {
        type: String,
        default: '20:00'
      },
      frequency: {
        type: Number, // minutes between buses
        min: 5,
        default: 30
      }
    }
  },
  routePath: {
    type: {
      type: String,
      enum: ['LineString'],
      default: 'LineString'
    },
    coordinates: {
      type: [[Number]], // Array of [longitude, latitude] pairs
      required: true
    }
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'maintenance', 'suspended'],
    default: 'active'
  },
  operatingDays: {
    monday: { type: Boolean, default: true },
    tuesday: { type: Boolean, default: true },
    wednesday: { type: Boolean, default: true },
    thursday: { type: Boolean, default: true },
    friday: { type: Boolean, default: true },
    saturday: { type: Boolean, default: true },
    sunday: { type: Boolean, default: true }
  },
  specialNotes: {
    type: String,
    trim: true
  },
  emergencyContacts: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    phone: {
      type: String,
      required: true,
      trim: true
    },
    role: {
      type: String,
      required: true,
      trim: true
    }
  }],
  averageSpeed: {
    type: Number,
    default: 25, // km/h
    min: 1,
    max: 100
  },
  trafficFactors: {
    peakHours: {
      morning: {
        start: { type: String, default: '07:00' },
        end: { type: String, default: '09:00' },
        delayFactor: { type: Number, default: 1.5 }
      },
      evening: {
        start: { type: String, default: '17:00' },
        end: { type: String, default: '19:00' },
        delayFactor: { type: Number, default: 1.4 }
      }
    },
    weatherImpact: {
      rain: { type: Number, default: 1.2 },
      snow: { type: Number, default: 1.8 },
      fog: { type: Number, default: 1.3 }
    }
  },
  accessibility: {
    wheelchairAccessible: {
      type: Boolean,
      default: false
    },
    audioAnnouncements: {
      type: Boolean,
      default: false
    },
    visualDisplays: {
      type: Boolean,
      default: false
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Create geospatial indexes
routeSchema.index({ 'startPoint.location': '2dsphere' });
routeSchema.index({ 'endPoint.location': '2dsphere' });
routeSchema.index({ 'routePath': '2dsphere' });

// Create compound indexes
routeSchema.index({ routeNumber: 1, status: 1 });
routeSchema.index({ status: 1, 'operatingDays.monday': 1 });

// Virtual for total stops count
routeSchema.virtual('totalStops').get(function() {
  return this.stops.length;
});

// Virtual for current operating status
routeSchema.virtual('isOperatingToday').get(function() {
  const today = new Date().toLocaleDateString('en-US', { weekday: 'lowercase' });
  return this.operatingDays[today] && this.status === 'active';
});

// Virtual for route direction
routeSchema.virtual('direction').get(function() {
  return `${this.startPoint.name} to ${this.endPoint.name}`;
});

// Pre-save middleware
routeSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Method to get current schedule based on day type
routeSchema.methods.getCurrentSchedule = function() {
  const now = new Date();
  const isWeekend = now.getDay() === 0 || now.getDay() === 6;
  
  // Check if it's a holiday (this would need to be enhanced with a holiday calendar)
  const isHoliday = false; // Placeholder
  
  if (isHoliday && this.schedule.holidays.firstBus) {
    return this.schedule.holidays;
  } else if (isWeekend) {
    return this.schedule.weekends;
  } else {
    return this.schedule.weekdays;
  }
};

// Method to calculate estimated travel time with traffic factors
routeSchema.methods.getEstimatedTravelTime = function(currentTime = new Date()) {
  let baseDuration = this.estimatedDuration;
  const timeString = currentTime.toTimeString().slice(0, 5);
  
  // Apply peak hour factors
  const peakHours = this.trafficFactors.peakHours;
  if (timeString >= peakHours.morning.start && timeString <= peakHours.morning.end) {
    baseDuration *= peakHours.morning.delayFactor;
  } else if (timeString >= peakHours.evening.start && timeString <= peakHours.evening.end) {
    baseDuration *= peakHours.evening.delayFactor;
  }
  
  return Math.round(baseDuration);
};

// Static method to find routes by location
routeSchema.statics.findByLocation = function(longitude, latitude, maxDistance = 1000) {
  return this.find({
    $or: [
      {
        'startPoint.location': {
          $near: {
            $geometry: { type: 'Point', coordinates: [longitude, latitude] },
            $maxDistance: maxDistance
          }
        }
      },
      {
        'endPoint.location': {
          $near: {
            $geometry: { type: 'Point', coordinates: [longitude, latitude] },
            $maxDistance: maxDistance
          }
        }
      }
    ],
    status: 'active'
  });
};

// Static method to find active routes
routeSchema.statics.findActive = function() {
  return this.find({ status: 'active' }).populate('stops');
};

module.exports = mongoose.model('Route', routeSchema);
