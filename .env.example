# Server Configuration
NODE_ENV=development
PORT=3000
API_VERSION=v1

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/bus_tracking
MONGODB_TEST_URI=mongodb://localhost:27017/bus_tracking_test

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your_refresh_token_secret_here
JWT_REFRESH_EXPIRES_IN=30d

# Encryption
ENCRYPTION_KEY=your_32_character_encryption_key

# Email Configuration (for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=<EMAIL>

# SMS Configuration (for emergency alerts)
SMS_API_KEY=your_sms_api_key
SMS_API_SECRET=your_sms_api_secret
SMS_FROM_NUMBER=+1234567890

# Push Notifications
FCM_SERVER_KEY=your_firebase_server_key
APNS_KEY_ID=your_apns_key_id
APNS_TEAM_ID=your_apns_team_id

# GPS and Mapping
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
MAPBOX_ACCESS_TOKEN=your_mapbox_access_token

# External APIs
WEATHER_API_KEY=your_weather_api_key
TRAFFIC_API_KEY=your_traffic_api_key

# Security
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN=http://localhost:3001

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=logs/app.log

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# Real-time Configuration
SOCKET_IO_CORS_ORIGIN=http://localhost:3001
GPS_UPDATE_INTERVAL=5000
PASSENGER_COUNT_UPDATE_INTERVAL=30000

# Geofencing
ROUTE_DEVIATION_THRESHOLD=500
SPEED_LIMIT_THRESHOLD=80
EMERGENCY_RESPONSE_TIME=300

# Analytics
ANALYTICS_RETENTION_DAYS=90
PERFORMANCE_METRICS_INTERVAL=60000

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=backups/

# Development
DEBUG_MODE=true
MOCK_GPS_DATA=false
ENABLE_API_DOCS=true
