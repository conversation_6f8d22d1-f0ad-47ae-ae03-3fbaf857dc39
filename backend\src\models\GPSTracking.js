const mongoose = require('mongoose');

const gpsTrackingSchema = new mongoose.Schema({
  bus: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Bus',
    required: true
  },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      required: true
    }
  },
  timestamp: {
    type: Date,
    required: true,
    default: Date.now
  },
  speed: {
    type: Number,
    default: 0,
    min: 0,
    max: 200 // km/h
  },
  heading: {
    type: Number,
    default: 0,
    min: 0,
    max: 360
  },
  accuracy: {
    type: Number,
    default: 0,
    min: 0
  },
  altitude: {
    type: Number,
    default: 0
  },
  route: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Route',
    required: true
  },
  nearestStop: {
    stop: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'BusStop'
    },
    distance: {
      type: Number, // meters
      min: 0
    }
  },
  routeProgress: {
    distanceCovered: {
      type: Number, // kilometers
      default: 0,
      min: 0
    },
    percentageComplete: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    estimatedTimeToDestination: {
      type: Number, // minutes
      default: 0,
      min: 0
    }
  },
  passengerCount: {
    current: {
      type: Number,
      default: 0,
      min: 0
    },
    capacity: {
      type: Number,
      required: true,
      min: 1
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },
  vehicleStatus: {
    engine: {
      type: String,
      enum: ['on', 'off', 'idle'],
      default: 'off'
    },
    doors: {
      type: String,
      enum: ['open', 'closed'],
      default: 'closed'
    },
    emergencyMode: {
      type: Boolean,
      default: false
    },
    maintenanceMode: {
      type: Boolean,
      default: false
    }
  },
  driverInfo: {
    driverId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    shiftStart: Date,
    shiftEnd: Date,
    breakStatus: {
      type: String,
      enum: ['active', 'on_break', 'off_duty'],
      default: 'active'
    }
  },
  environmentalData: {
    temperature: Number, // Celsius
    humidity: Number, // Percentage
    airQuality: {
      type: String,
      enum: ['good', 'moderate', 'poor', 'hazardous']
    },
    weather: {
      type: String,
      enum: ['clear', 'cloudy', 'rainy', 'snowy', 'foggy', 'stormy']
    }
  },
  alerts: [{
    type: {
      type: String,
      enum: ['speed_violation', 'route_deviation', 'emergency', 'maintenance', 'passenger_limit', 'schedule_delay'],
      required: true
    },
    severity: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      required: true
    },
    message: {
      type: String,
      required: true
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    acknowledged: {
      type: Boolean,
      default: false
    },
    acknowledgedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    acknowledgedAt: Date
  }],
  geofencing: {
    isOnRoute: {
      type: Boolean,
      default: true
    },
    deviationDistance: {
      type: Number, // meters
      default: 0
    },
    lastOnRouteTime: {
      type: Date,
      default: Date.now
    }
  },
  networkInfo: {
    signalStrength: {
      type: Number,
      min: 0,
      max: 100
    },
    networkType: {
      type: String,
      enum: ['2G', '3G', '4G', '5G', 'WiFi']
    },
    dataUsage: {
      type: Number, // MB
      default: 0
    }
  },
  batteryLevel: {
    type: Number,
    min: 0,
    max: 100
  },
  source: {
    type: String,
    enum: ['gps_device', 'mobile_app', 'obd_port', 'manual'],
    default: 'gps_device'
  },
  processed: {
    type: Boolean,
    default: false
  },
  processingErrors: [{
    error: String,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Create indexes for efficient queries
gpsTrackingSchema.index({ bus: 1, timestamp: -1 });
gpsTrackingSchema.index({ location: '2dsphere' });
gpsTrackingSchema.index({ route: 1, timestamp: -1 });
gpsTrackingSchema.index({ timestamp: -1 });
gpsTrackingSchema.index({ processed: 1, timestamp: 1 });
gpsTrackingSchema.index({ 'alerts.type': 1, 'alerts.acknowledged': 1 });

// TTL index to automatically delete old tracking data (keep for 30 days)
gpsTrackingSchema.index({ timestamp: 1 }, { expireAfterSeconds: 30 * 24 * 60 * 60 });

// Virtual for occupancy percentage
gpsTrackingSchema.virtual('occupancyPercentage').get(function() {
  return Math.round((this.passengerCount.current / this.passengerCount.capacity) * 100);
});

// Virtual for available seats
gpsTrackingSchema.virtual('availableSeats').get(function() {
  return this.passengerCount.capacity - this.passengerCount.current;
});

// Virtual for data age in minutes
gpsTrackingSchema.virtual('dataAgeMinutes').get(function() {
  return Math.round((Date.now() - this.timestamp.getTime()) / (1000 * 60));
});

// Virtual for alert count by severity
gpsTrackingSchema.virtual('alertCounts').get(function() {
  const counts = { low: 0, medium: 0, high: 0, critical: 0 };
  this.alerts.forEach(alert => {
    if (!alert.acknowledged) {
      counts[alert.severity]++;
    }
  });
  return counts;
});

// Pre-save middleware to process location data
gpsTrackingSchema.pre('save', async function(next) {
  if (this.isNew || this.isModified('location')) {
    try {
      // Calculate nearest stop
      const BusStop = mongoose.model('BusStop');
      const nearestStops = await BusStop.aggregate([
        {
          $geoNear: {
            near: {
              type: 'Point',
              coordinates: this.location.coordinates
            },
            distanceField: 'distance',
            maxDistance: 1000, // 1km
            spherical: true,
            query: { status: 'active' }
          }
        },
        { $limit: 1 }
      ]);
      
      if (nearestStops.length > 0) {
        this.nearestStop = {
          stop: nearestStops[0]._id,
          distance: Math.round(nearestStops[0].distance)
        };
      }
      
      // Check geofencing
      const Route = mongoose.model('Route');
      const route = await Route.findById(this.route);
      if (route) {
        // Simplified geofencing check - in reality, you'd use more sophisticated algorithms
        const routeBuffer = 200; // 200 meters buffer
        this.geofencing.isOnRoute = this.nearestStop && this.nearestStop.distance <= routeBuffer;
        
        if (!this.geofencing.isOnRoute) {
          this.geofencing.deviationDistance = this.nearestStop ? this.nearestStop.distance : 999999;
          
          // Add route deviation alert
          this.alerts.push({
            type: 'route_deviation',
            severity: this.geofencing.deviationDistance > 500 ? 'high' : 'medium',
            message: `Bus has deviated ${this.geofencing.deviationDistance}m from route`,
            timestamp: new Date()
          });
        } else {
          this.geofencing.lastOnRouteTime = new Date();
        }
      }
      
      // Speed violation check
      if (this.speed > 60) { // 60 km/h speed limit
        this.alerts.push({
          type: 'speed_violation',
          severity: this.speed > 80 ? 'critical' : 'high',
          message: `Speed violation: ${this.speed} km/h`,
          timestamp: new Date()
        });
      }
      
      // Passenger limit check
      if (this.passengerCount.current > this.passengerCount.capacity) {
        this.alerts.push({
          type: 'passenger_limit',
          severity: 'high',
          message: `Passenger count exceeds capacity: ${this.passengerCount.current}/${this.passengerCount.capacity}`,
          timestamp: new Date()
        });
      }
      
    } catch (error) {
      this.processingErrors.push({
        error: error.message,
        timestamp: new Date()
      });
    }
  }
  
  next();
});

// Method to acknowledge alert
gpsTrackingSchema.methods.acknowledgeAlert = function(alertId, userId) {
  const alert = this.alerts.id(alertId);
  if (alert) {
    alert.acknowledged = true;
    alert.acknowledgedBy = userId;
    alert.acknowledgedAt = new Date();
    return this.save();
  }
  throw new Error('Alert not found');
};

// Static method to get latest tracking data for a bus
gpsTrackingSchema.statics.getLatestForBus = function(busId) {
  return this.findOne({ bus: busId })
    .sort({ timestamp: -1 })
    .populate('bus')
    .populate('route')
    .populate('nearestStop.stop');
};

// Static method to get tracking history for a bus
gpsTrackingSchema.statics.getHistoryForBus = function(busId, startDate, endDate, limit = 100) {
  const query = { bus: busId };
  
  if (startDate || endDate) {
    query.timestamp = {};
    if (startDate) query.timestamp.$gte = new Date(startDate);
    if (endDate) query.timestamp.$lte = new Date(endDate);
  }
  
  return this.find(query)
    .sort({ timestamp: -1 })
    .limit(limit)
    .populate('nearestStop.stop');
};

// Static method to get buses in area
gpsTrackingSchema.statics.getBusesInArea = function(longitude, latitude, radius = 1000) {
  return this.aggregate([
    {
      $geoNear: {
        near: {
          type: 'Point',
          coordinates: [longitude, latitude]
        },
        distanceField: 'distance',
        maxDistance: radius,
        spherical: true
      }
    },
    {
      $group: {
        _id: '$bus',
        latestTracking: { $first: '$$ROOT' }
      }
    },
    {
      $replaceRoot: { newRoot: '$latestTracking' }
    },
    {
      $lookup: {
        from: 'buses',
        localField: 'bus',
        foreignField: '_id',
        as: 'busInfo'
      }
    },
    {
      $lookup: {
        from: 'routes',
        localField: 'route',
        foreignField: '_id',
        as: 'routeInfo'
      }
    }
  ]);
};

module.exports = mongoose.model('GPSTracking', gpsTrackingSchema);
