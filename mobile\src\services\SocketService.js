import io from 'socket.io-client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_BASE_URL } from '../config/constants';

class SocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.eventListeners = new Map();
    this.subscriptions = new Set();
  }

  /**
   * Connect to the Socket.IO server
   */
  async connect() {
    try {
      if (this.socket && this.isConnected) {
        console.log('Socket already connected');
        return;
      }

      const token = await AsyncStorage.getItem('authToken');
      
      this.socket = io(API_BASE_URL, {
        transports: ['websocket'],
        timeout: 10000,
        auth: {
          token: token || null,
        },
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
      });

      this.setupEventHandlers();
      
      console.log('Attempting to connect to Socket.IO server...');
    } catch (error) {
      console.error('Error connecting to socket:', error);
    }
  }

  /**
   * Disconnect from the Socket.IO server
   */
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.subscriptions.clear();
      console.log('Disconnected from Socket.IO server');
    }
  }

  /**
   * Setup event handlers for socket connection
   */
  setupEventHandlers() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Connected to Socket.IO server');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      // Resubscribe to previous subscriptions
      this.resubscribe();
      
      // Start heartbeat
      this.startHeartbeat();
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected from Socket.IO server:', reason);
      this.isConnected = false;
      
      // Stop heartbeat
      this.stopHeartbeat();
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      this.isConnected = false;
      this.reconnectAttempts++;
      
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.error('Max reconnection attempts reached');
        this.disconnect();
      }
    });

    this.socket.on('reconnect', (attemptNumber) => {
      console.log(`Reconnected to Socket.IO server (attempt ${attemptNumber})`);
      this.isConnected = true;
      this.reconnectAttempts = 0;
    });

    this.socket.on('error', (error) => {
      console.error('Socket error:', error);
    });

    // Handle server responses
    this.socket.on('bus:location:update:ack', (data) => {
      console.log('Location update acknowledged:', data);
    });

    this.socket.on('bus:passengers:update:ack', (data) => {
      console.log('Passenger update acknowledged:', data);
    });

    this.socket.on('emergency:alert:ack', (data) => {
      console.log('Emergency alert acknowledged:', data);
    });
  }

  /**
   * Subscribe to route updates
   */
  subscribeToRoute(routeId) {
    if (!this.socket || !routeId) return;
    
    const subscription = `route:${routeId}`;
    if (this.subscriptions.has(subscription)) {
      console.log(`Already subscribed to route ${routeId}`);
      return;
    }

    this.socket.emit('subscribe:route', routeId);
    this.subscriptions.add(subscription);
    console.log(`Subscribed to route ${routeId}`);
  }

  /**
   * Unsubscribe from route updates
   */
  unsubscribeFromRoute(routeId) {
    if (!this.socket || !routeId) return;
    
    const subscription = `route:${routeId}`;
    if (!this.subscriptions.has(subscription)) {
      console.log(`Not subscribed to route ${routeId}`);
      return;
    }

    this.socket.emit('unsubscribe:route', routeId);
    this.subscriptions.delete(subscription);
    console.log(`Unsubscribed from route ${routeId}`);
  }

  /**
   * Subscribe to bus updates
   */
  subscribeToBus(busId) {
    if (!this.socket || !busId) return;
    
    const subscription = `bus:${busId}`;
    if (this.subscriptions.has(subscription)) {
      console.log(`Already subscribed to bus ${busId}`);
      return;
    }

    this.socket.emit('subscribe:bus', busId);
    this.subscriptions.add(subscription);
    console.log(`Subscribed to bus ${busId}`);
  }

  /**
   * Unsubscribe from bus updates
   */
  unsubscribeFromBus(busId) {
    if (!this.socket || !busId) return;
    
    const subscription = `bus:${busId}`;
    if (!this.subscriptions.has(subscription)) {
      console.log(`Not subscribed to bus ${busId}`);
      return;
    }

    this.socket.emit('unsubscribe:bus', busId);
    this.subscriptions.delete(subscription);
    console.log(`Unsubscribed from bus ${busId}`);
  }

  /**
   * Subscribe to stop updates
   */
  subscribeToStop(stopId) {
    if (!this.socket || !stopId) return;
    
    const subscription = `stop:${stopId}`;
    if (this.subscriptions.has(subscription)) {
      console.log(`Already subscribed to stop ${stopId}`);
      return;
    }

    this.socket.emit('subscribe:stop', stopId);
    this.subscriptions.add(subscription);
    console.log(`Subscribed to stop ${stopId}`);
  }

  /**
   * Subscribe to area updates (for nearby buses)
   */
  subscribeToArea(latitude, longitude, radius = 1000) {
    if (!this.socket || !latitude || !longitude) return;
    
    const subscription = `area:${latitude}:${longitude}:${radius}`;
    if (this.subscriptions.has(subscription)) {
      console.log(`Already subscribed to area updates`);
      return;
    }

    this.socket.emit('subscribe:area', { latitude, longitude, radius });
    this.subscriptions.add(subscription);
    console.log(`Subscribed to area updates`);
  }

  /**
   * Send bus location update (for driver/conductor app)
   */
  updateBusLocation(busId, locationData) {
    if (!this.socket || !busId || !locationData) return;

    this.socket.emit('bus:location:update', {
      busId,
      ...locationData,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send passenger count update (for driver/conductor app)
   */
  updatePassengerCount(busId, count, capacity) {
    if (!this.socket || !busId || count === undefined) return;

    this.socket.emit('bus:passengers:update', {
      busId,
      count,
      capacity,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Send emergency alert
   */
  sendEmergencyAlert(busId, alertData) {
    if (!this.socket || !busId || !alertData) return;

    this.socket.emit('emergency:alert', {
      busId,
      ...alertData,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Add event listener
   */
  on(event, callback) {
    if (!this.socket) return;

    // Store the callback for potential resubscription
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event).add(callback);

    this.socket.on(event, callback);
  }

  /**
   * Remove event listener
   */
  off(event, callback) {
    if (!this.socket) return;

    this.socket.off(event, callback);

    // Remove from stored callbacks
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).delete(callback);
      
      if (this.eventListeners.get(event).size === 0) {
        this.eventListeners.delete(event);
      }
    }
  }

  /**
   * Emit event to server
   */
  emit(event, data) {
    if (!this.socket) return;
    
    this.socket.emit(event, data);
  }

  /**
   * Resubscribe to all previous subscriptions
   */
  resubscribe() {
    this.subscriptions.forEach(subscription => {
      const [type, ...params] = subscription.split(':');
      
      switch (type) {
        case 'route':
          this.socket.emit('subscribe:route', params[0]);
          break;
        case 'bus':
          this.socket.emit('subscribe:bus', params[0]);
          break;
        case 'stop':
          this.socket.emit('subscribe:stop', params[0]);
          break;
        case 'area':
          this.socket.emit('subscribe:area', {
            latitude: parseFloat(params[0]),
            longitude: parseFloat(params[1]),
            radius: parseInt(params[2]),
          });
          break;
      }
    });

    // Re-add event listeners
    this.eventListeners.forEach((callbacks, event) => {
      callbacks.forEach(callback => {
        this.socket.on(event, callback);
      });
    });

    console.log(`Resubscribed to ${this.subscriptions.size} subscriptions`);
  }

  /**
   * Start heartbeat to keep connection alive
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.socket && this.isConnected) {
        this.socket.emit('heartbeat');
      }
    }, 30000); // Send heartbeat every 30 seconds
  }

  /**
   * Stop heartbeat
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * Get connection status
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      subscriptions: Array.from(this.subscriptions),
      eventListeners: Array.from(this.eventListeners.keys()),
    };
  }

  /**
   * Clear all subscriptions and listeners
   */
  clearAll() {
    this.subscriptions.clear();
    this.eventListeners.clear();
    
    if (this.socket) {
      this.socket.removeAllListeners();
    }
  }
}

export default new SocketService();
