import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_BASE_URL } from '../config/constants';

class BusService {
  constructor() {
    this.apiClient = axios.create({
      baseURL: `${API_BASE_URL}/api/v1`,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add request interceptor to include auth token
    this.apiClient.interceptors.request.use(
      async (config) => {
        const token = await AsyncStorage.getItem('authToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Add response interceptor for error handling
    this.apiClient.interceptors.response.use(
      (response) => response.data,
      (error) => {
        if (error.response?.status === 401) {
          // <PERSON>le unauthorized access
          this.handleUnauthorized();
        }
        return Promise.reject(error);
      }
    );
  }

  async handleUnauthorized() {
    await AsyncStorage.removeItem('authToken');
    await AsyncStorage.removeItem('userProfile');
    // Navigate to login screen - this would be handled by navigation service
  }

  /**
   * Get all buses with optional filtering
   */
  async getAllBuses(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.route) params.append('route', filters.route);
      if (filters.status) params.append('status', filters.status);
      if (filters.page) params.append('page', filters.page);
      if (filters.limit) params.append('limit', filters.limit);

      const response = await this.apiClient.get(`/buses?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching buses:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get bus details by ID
   */
  async getBusById(busId) {
    try {
      const response = await this.apiClient.get(`/buses/${busId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching bus details:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get current location of a bus
   */
  async getBusLocation(busId) {
    try {
      const response = await this.apiClient.get(`/buses/${busId}/location`);
      return response.data;
    } catch (error) {
      console.error('Error fetching bus location:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get passenger count for a bus
   */
  async getPassengerCount(busId) {
    try {
      const response = await this.apiClient.get(`/buses/${busId}/passengers`);
      return response.data;
    } catch (error) {
      console.error('Error fetching passenger count:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Find buses near a location
   */
  async getNearbyBuses(longitude, latitude, radius = 1000) {
    try {
      const params = new URLSearchParams({
        longitude: longitude.toString(),
        latitude: latitude.toString(),
        radius: radius.toString(),
      });

      const response = await this.apiClient.get(`/buses/nearby?${params.toString()}`);
      return response.data.buses || [];
    } catch (error) {
      console.error('Error fetching nearby buses:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get all buses on a specific route
   */
  async getBusesByRoute(routeId) {
    try {
      const response = await this.apiClient.get(`/buses/route/${routeId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching buses by route:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get all routes
   */
  async getAllRoutes(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.status) params.append('status', filters.status);
      if (filters.page) params.append('page', filters.page);
      if (filters.limit) params.append('limit', filters.limit);

      const response = await this.apiClient.get(`/routes?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching routes:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get route details by ID
   */
  async getRouteById(routeId) {
    try {
      const response = await this.apiClient.get(`/routes/${routeId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching route details:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Find routes near a location
   */
  async getNearbyRoutes(longitude, latitude, radius = 1000) {
    try {
      const params = new URLSearchParams({
        longitude: longitude.toString(),
        latitude: latitude.toString(),
        radius: radius.toString(),
      });

      const response = await this.apiClient.get(`/routes/nearby?${params.toString()}`);
      return response.data.routes || [];
    } catch (error) {
      console.error('Error fetching nearby routes:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get all bus stops
   */
  async getAllStops(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.zone) params.append('zone', filters.zone);
      if (filters.route) params.append('route', filters.route);
      if (filters.page) params.append('page', filters.page);
      if (filters.limit) params.append('limit', filters.limit);

      const response = await this.apiClient.get(`/stops?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching stops:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get stop details by ID
   */
  async getStopById(stopId) {
    try {
      const response = await this.apiClient.get(`/stops/${stopId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching stop details:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Find bus stops near a location
   */
  async getNearbyStops(longitude, latitude, radius = 500) {
    try {
      const params = new URLSearchParams({
        longitude: longitude.toString(),
        latitude: latitude.toString(),
        radius: radius.toString(),
      });

      const response = await this.apiClient.get(`/stops/nearby?${params.toString()}`);
      return response.data.stops || [];
    } catch (error) {
      console.error('Error fetching nearby stops:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get next buses for a stop
   */
  async getNextBuses(stopId, limit = 5) {
    try {
      const params = new URLSearchParams({
        limit: limit.toString(),
      });

      const response = await this.apiClient.get(`/stops/${stopId}/next-buses?${params.toString()}`);
      return response.data.buses || [];
    } catch (error) {
      console.error('Error fetching next buses:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Search for routes, stops, or destinations
   */
  async search(query, type = 'all') {
    try {
      const params = new URLSearchParams({
        q: query,
        type: type,
      });

      const response = await this.apiClient.get(`/search?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error searching:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get route planning between two points
   */
  async getRoutePlanning(fromLat, fromLng, toLat, toLng, options = {}) {
    try {
      const params = new URLSearchParams({
        fromLat: fromLat.toString(),
        fromLng: fromLng.toString(),
        toLat: toLat.toString(),
        toLng: toLng.toString(),
        ...options,
      });

      const response = await this.apiClient.get(`/route-planning?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error getting route planning:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get user's favorite routes
   */
  async getFavoriteRoutes() {
    try {
      const response = await this.apiClient.get('/users/favorites/routes');
      return response.data.routes || [];
    } catch (error) {
      console.error('Error fetching favorite routes:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Add route to favorites
   */
  async addFavoriteRoute(routeId, nickname = '') {
    try {
      const response = await this.apiClient.post('/users/favorites/routes', {
        routeId,
        nickname,
      });
      return response.data;
    } catch (error) {
      console.error('Error adding favorite route:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Remove route from favorites
   */
  async removeFavoriteRoute(routeId) {
    try {
      const response = await this.apiClient.delete(`/users/favorites/routes/${routeId}`);
      return response.data;
    } catch (error) {
      console.error('Error removing favorite route:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get user's favorite stops
   */
  async getFavoriteStops() {
    try {
      const response = await this.apiClient.get('/users/favorites/stops');
      return response.data.stops || [];
    } catch (error) {
      console.error('Error fetching favorite stops:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Add stop to favorites
   */
  async addFavoriteStop(stopId, nickname = '') {
    try {
      const response = await this.apiClient.post('/users/favorites/stops', {
        stopId,
        nickname,
      });
      return response.data;
    } catch (error) {
      console.error('Error adding favorite stop:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Remove stop from favorites
   */
  async removeFavoriteStop(stopId) {
    try {
      const response = await this.apiClient.delete(`/users/favorites/stops/${stopId}`);
      return response.data;
    } catch (error) {
      console.error('Error removing favorite stop:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Report an issue with a bus or service
   */
  async reportIssue(data) {
    try {
      const response = await this.apiClient.post('/reports/issue', data);
      return response.data;
    } catch (error) {
      console.error('Error reporting issue:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Submit feedback or rating
   */
  async submitFeedback(data) {
    try {
      const response = await this.apiClient.post('/feedback', data);
      return response.data;
    } catch (error) {
      console.error('Error submitting feedback:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Get service announcements
   */
  async getAnnouncements() {
    try {
      const response = await this.apiClient.get('/announcements');
      return response.data.announcements || [];
    } catch (error) {
      console.error('Error fetching announcements:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Handle API errors
   */
  handleError(error) {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      return {
        message: data.message || 'An error occurred',
        status,
        details: data.details || null,
      };
    } else if (error.request) {
      // Network error
      return {
        message: 'Network error. Please check your connection.',
        status: 0,
        details: null,
      };
    } else {
      // Other error
      return {
        message: error.message || 'An unexpected error occurred',
        status: -1,
        details: null,
      };
    }
  }
}

export default new BusService();
