<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Government Bus Tracking System - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #3498db;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .feature-card ul {
            list-style: none;
            padding-left: 0;
        }
        
        .feature-card li {
            padding: 5px 0;
            color: #555;
        }
        
        .feature-card li:before {
            content: "✓ ";
            color: #27ae60;
            font-weight: bold;
        }
        
        .demo-section {
            background: #ecf0f1;
            padding: 30px;
            border-radius: 10px;
            margin: 30px 0;
        }
        
        .demo-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .bus-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .bus-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .bus-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 10px;
        }
        
        .bus-route {
            color: #7f8c8d;
            margin-bottom: 15px;
        }
        
        .bus-status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .status-active {
            background: #d5f4e6;
            color: #27ae60;
        }
        
        .passenger-info {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #ecf0f1;
        }
        
        .api-section {
            background: #2c3e50;
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin: 30px 0;
        }
        
        .api-section h2 {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .api-endpoints {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .endpoint {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        
        .endpoint-method {
            color: #e74c3c;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .endpoint-url {
            color: #f39c12;
            font-family: monospace;
        }
        
        .footer {
            background: #34495e;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        .status-online {
            background: #27ae60;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚌 Government Bus Tracking System</h1>
            <p>Real-time GPS tracking with passenger information and safety features</p>
            <div style="margin-top: 15px;">
                <span class="status-indicator status-online"></span>
                <span>System Status: Online</span>
            </div>
        </div>
        
        <div class="content">
            <div class="features">
                <div class="feature-card">
                    <h3>🗺️ Real-time Tracking</h3>
                    <ul>
                        <li>Live GPS location updates</li>
                        <li>Accurate arrival time predictions</li>
                        <li>Route progress monitoring</li>
                        <li>Interactive map interface</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>👥 Passenger Information</h3>
                    <ul>
                        <li>Current seat availability</li>
                        <li>Real-time passenger counts</li>
                        <li>Occupancy percentage</li>
                        <li>Accessibility information</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🛡️ Safety & Security</h3>
                    <ul>
                        <li>Emergency alert system</li>
                        <li>Geofencing monitoring</li>
                        <li>Speed violation detection</li>
                        <li>Multi-channel notifications</li>
                    </ul>
                </div>
            </div>
            
            <div class="demo-section">
                <h2>🚌 Live Bus Status Demo</h2>
                <div class="bus-list">
                    <div class="bus-card">
                        <div class="bus-number">BUS001</div>
                        <div class="bus-route">City Center ↔ Airport Terminal</div>
                        <div class="bus-status status-active">Active</div>
                        <div class="passenger-info">
                            <span>👥 25/50 passengers</span>
                            <span>🪑 25 seats available</span>
                        </div>
                    </div>
                    
                    <div class="bus-card">
                        <div class="bus-number">BUS002</div>
                        <div class="bus-route">Downtown ↔ University Campus</div>
                        <div class="bus-status status-active">Active</div>
                        <div class="passenger-info">
                            <span>👥 18/40 passengers</span>
                            <span>🪑 22 seats available</span>
                        </div>
                    </div>
                    
                    <div class="bus-card">
                        <div class="bus-number">BUS003</div>
                        <div class="bus-route">Mall District ↔ Hospital</div>
                        <div class="bus-status status-active">Active</div>
                        <div class="passenger-info">
                            <span>👥 32/45 passengers</span>
                            <span>🪑 13 seats available</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="api-section">
                <h2>🔌 API Endpoints</h2>
                <div class="api-endpoints">
                    <div class="endpoint">
                        <div><span class="endpoint-method">GET</span><span class="endpoint-url">/health</span></div>
                        <div style="margin-top: 8px; font-size: 0.9em;">System health check</div>
                    </div>
                    
                    <div class="endpoint">
                        <div><span class="endpoint-method">GET</span><span class="endpoint-url">/api/v1/buses</span></div>
                        <div style="margin-top: 8px; font-size: 0.9em;">Get all buses with real-time data</div>
                    </div>
                    
                    <div class="endpoint">
                        <div><span class="endpoint-method">GET</span><span class="endpoint-url">/api/v1/routes</span></div>
                        <div style="margin-top: 8px; font-size: 0.9em;">Get all available routes</div>
                    </div>
                    
                    <div class="endpoint">
                        <div><span class="endpoint-method">GET</span><span class="endpoint-url">/api/v1/buses/nearby</span></div>
                        <div style="margin-top: 8px; font-size: 0.9em;">Find buses near location</div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 40px 0;">
                <h2>🚀 Getting Started</h2>
                <p style="margin: 20px 0; color: #555; font-size: 1.1em;">
                    The Bus Tracking System is now ready! Here's how to use it:
                </p>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: left; max-width: 600px; margin: 0 auto;">
                    <h3 style="color: #2c3e50; margin-bottom: 15px;">📱 For Passengers:</h3>
                    <ul style="color: #555; line-height: 1.8;">
                        <li>Open the mobile app to view real-time bus locations</li>
                        <li>Check arrival times and seat availability</li>
                        <li>Set up notifications for your favorite routes</li>
                        <li>Use the emergency alert feature if needed</li>
                    </ul>
                    
                    <h3 style="color: #2c3e50; margin: 20px 0 15px 0;">🚌 For Bus Staff:</h3>
                    <ul style="color: #555; line-height: 1.8;">
                        <li>Use the driver interface to update passenger counts</li>
                        <li>Report issues or emergencies through the app</li>
                        <li>Monitor route compliance and schedules</li>
                    </ul>
                    
                    <h3 style="color: #2c3e50; margin: 20px 0 15px 0;">👨‍💼 For Administrators:</h3>
                    <ul style="color: #555; line-height: 1.8;">
                        <li>Access the web dashboard for fleet management</li>
                        <li>Monitor all buses and routes in real-time</li>
                        <li>View analytics and performance reports</li>
                        <li>Manage emergency responses and alerts</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>🏛️ Government Bus Tracking System | Built with Node.js, React Native, and MongoDB</p>
            <p style="margin-top: 10px; opacity: 0.8;">Enhancing public transportation with real-time technology</p>
        </div>
    </div>
    
    <script>
        // Simple demo script to simulate real-time updates
        function updateTimestamp() {
            const now = new Date().toLocaleString();
            console.log('Bus Tracking System Demo - Current time:', now);
        }
        
        // Update every 30 seconds
        setInterval(updateTimestamp, 30000);
        updateTimestamp();
        
        // Log system info
        console.log('🚌 Government Bus Tracking System Demo');
        console.log('📅 Loaded at:', new Date().toISOString());
        console.log('🌐 User Agent:', navigator.userAgent);
        console.log('📱 Screen:', screen.width + 'x' + screen.height);
    </script>
</body>
</html>
