const logger = require('../utils/logger');

class RealTimeService {
  constructor() {
    this.io = null;
    this.connectedClients = new Map();
    this.subscriptions = new Map(); // Track client subscriptions
  }

  /**
   * Initialize the real-time service with Socket.IO instance
   */
  initialize(io) {
    this.io = io;
    this.setupSocketHandlers();
    logger.info('Real-time service initialized');
  }

  /**
   * Setup Socket.IO event handlers
   */
  setupSocketHandlers() {
    if (!this.io) return;

    this.io.on('connection', (socket) => {
      logger.info(`Client connected: ${socket.id}`);
      
      // Store client information
      this.connectedClients.set(socket.id, {
        socket,
        subscriptions: new Set(),
        connectedAt: new Date(),
        lastActivity: new Date()
      });

      // Handle client subscriptions
      this.handleSubscriptions(socket);
      
      // Handle GPS updates from buses
      this.handleGPSUpdates(socket);
      
      // Handle passenger updates
      this.handlePassengerUpdates(socket);
      
      // Handle emergency alerts
      this.handleEmergencyAlerts(socket);
      
      // Handle disconnection
      socket.on('disconnect', () => {
        this.handleDisconnection(socket.id);
      });

      // Handle heartbeat for connection monitoring
      socket.on('heartbeat', () => {
        const client = this.connectedClients.get(socket.id);
        if (client) {
          client.lastActivity = new Date();
        }
      });
    });

    // Start connection monitoring
    this.startConnectionMonitoring();
  }

  /**
   * Handle client subscriptions to routes and buses
   */
  handleSubscriptions(socket) {
    const client = this.connectedClients.get(socket.id);
    if (!client) return;

    // Subscribe to route updates
    socket.on('subscribe:route', (routeId) => {
      if (!routeId) return;
      
      const roomName = `route:${routeId}`;
      socket.join(roomName);
      client.subscriptions.add(roomName);
      
      logger.debug(`Client ${socket.id} subscribed to route ${routeId}`);
      
      // Send current route status
      this.sendRouteStatus(socket, routeId);
    });

    // Unsubscribe from route updates
    socket.on('unsubscribe:route', (routeId) => {
      if (!routeId) return;
      
      const roomName = `route:${routeId}`;
      socket.leave(roomName);
      client.subscriptions.delete(roomName);
      
      logger.debug(`Client ${socket.id} unsubscribed from route ${routeId}`);
    });

    // Subscribe to bus updates
    socket.on('subscribe:bus', (busId) => {
      if (!busId) return;
      
      const roomName = `bus:${busId}`;
      socket.join(roomName);
      client.subscriptions.add(roomName);
      
      logger.debug(`Client ${socket.id} subscribed to bus ${busId}`);
      
      // Send current bus status
      this.sendBusStatus(socket, busId);
    });

    // Unsubscribe from bus updates
    socket.on('unsubscribe:bus', (busId) => {
      if (!busId) return;
      
      const roomName = `bus:${busId}`;
      socket.leave(roomName);
      client.subscriptions.delete(roomName);
      
      logger.debug(`Client ${socket.id} unsubscribed from bus ${busId}`);
    });

    // Subscribe to stop updates
    socket.on('subscribe:stop', (stopId) => {
      if (!stopId) return;
      
      const roomName = `stop:${stopId}`;
      socket.join(roomName);
      client.subscriptions.add(roomName);
      
      logger.debug(`Client ${socket.id} subscribed to stop ${stopId}`);
    });

    // Subscribe to area updates (for nearby buses)
    socket.on('subscribe:area', (data) => {
      const { latitude, longitude, radius } = data;
      if (!latitude || !longitude) return;
      
      const roomName = `area:${latitude}:${longitude}:${radius || 1000}`;
      socket.join(roomName);
      client.subscriptions.add(roomName);
      
      logger.debug(`Client ${socket.id} subscribed to area updates`);
    });
  }

  /**
   * Handle GPS updates from buses
   */
  handleGPSUpdates(socket) {
    socket.on('bus:location:update', async (data) => {
      try {
        const { busId, location, speed, heading, accuracy, timestamp } = data;
        
        if (!busId || !location) {
          socket.emit('error', { message: 'Invalid GPS data' });
          return;
        }

        // Validate the client has permission to update this bus
        // This would typically check if the socket is authenticated as the bus driver/system
        
        // Broadcast to all clients subscribed to this bus
        this.broadcastBusLocationUpdate(busId, {
          location,
          speed,
          heading,
          accuracy,
          timestamp: timestamp || new Date()
        });

        // Also broadcast to route subscribers
        const Bus = require('../models/Bus');
        const bus = await Bus.findById(busId).select('route');
        if (bus && bus.route) {
          this.broadcastToRoute(bus.route, 'bus:location:updated', {
            busId,
            location,
            speed,
            heading,
            accuracy,
            timestamp: timestamp || new Date()
          });
        }

        socket.emit('bus:location:update:ack', { success: true });
        
      } catch (error) {
        logger.error('Error handling GPS update:', error);
        socket.emit('error', { message: 'Failed to process GPS update' });
      }
    });
  }

  /**
   * Handle passenger count updates
   */
  handlePassengerUpdates(socket) {
    socket.on('bus:passengers:update', async (data) => {
      try {
        const { busId, count, capacity } = data;
        
        if (!busId || count === undefined) {
          socket.emit('error', { message: 'Invalid passenger data' });
          return;
        }

        // Broadcast passenger count update
        this.broadcastPassengerCountUpdate(busId, {
          currentPassengerCount: count,
          availableSeats: capacity ? capacity - count : null,
          occupancyPercentage: capacity ? Math.round((count / capacity) * 100) : null,
          timestamp: new Date()
        });

        socket.emit('bus:passengers:update:ack', { success: true });
        
      } catch (error) {
        logger.error('Error handling passenger update:', error);
        socket.emit('error', { message: 'Failed to process passenger update' });
      }
    });
  }

  /**
   * Handle emergency alerts
   */
  handleEmergencyAlerts(socket) {
    socket.on('emergency:alert', async (data) => {
      try {
        const { busId, type, message, severity, location } = data;
        
        if (!busId || !type || !message) {
          socket.emit('error', { message: 'Invalid emergency alert data' });
          return;
        }

        const alertData = {
          busId,
          type,
          message,
          severity: severity || 'high',
          location,
          timestamp: new Date(),
          alertId: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        };

        // Broadcast emergency alert to all clients
        this.broadcastEmergencyAlert(alertData);

        // Log the emergency alert
        logger.warn(`Emergency alert from bus ${busId}: ${message}`, alertData);

        socket.emit('emergency:alert:ack', { success: true, alertId: alertData.alertId });
        
      } catch (error) {
        logger.error('Error handling emergency alert:', error);
        socket.emit('error', { message: 'Failed to process emergency alert' });
      }
    });
  }

  /**
   * Handle client disconnection
   */
  handleDisconnection(socketId) {
    const client = this.connectedClients.get(socketId);
    if (client) {
      const connectionDuration = Date.now() - client.connectedAt.getTime();
      logger.info(`Client disconnected: ${socketId} (connected for ${Math.round(connectionDuration / 1000)}s)`);
      
      // Clean up subscriptions
      client.subscriptions.clear();
      this.connectedClients.delete(socketId);
    }
  }

  /**
   * Broadcast bus location update
   */
  broadcastBusLocationUpdate(busId, data) {
    if (!this.io) return;
    
    const roomName = `bus:${busId}`;
    this.io.to(roomName).emit('bus:location:updated', {
      busId,
      ...data
    });
    
    logger.debug(`Broadcasted location update for bus ${busId}`);
  }

  /**
   * Broadcast passenger count update
   */
  broadcastPassengerCountUpdate(busId, data) {
    if (!this.io) return;
    
    const roomName = `bus:${busId}`;
    this.io.to(roomName).emit('bus:passengers:updated', {
      busId,
      ...data
    });
    
    logger.debug(`Broadcasted passenger count update for bus ${busId}`);
  }

  /**
   * Broadcast bus status update
   */
  broadcastBusStatusUpdate(busId, data) {
    if (!this.io) return;
    
    const roomName = `bus:${busId}`;
    this.io.to(roomName).emit('bus:status:updated', {
      busId,
      ...data
    });
    
    logger.debug(`Broadcasted status update for bus ${busId}`);
  }

  /**
   * Broadcast ETA update
   */
  broadcastETAUpdate(busId, data) {
    if (!this.io) return;
    
    const roomName = `bus:${busId}`;
    this.io.to(roomName).emit('bus:eta:updated', {
      busId,
      ...data
    });
  }

  /**
   * Broadcast to all clients subscribed to a route
   */
  broadcastToRoute(routeId, event, data) {
    if (!this.io) return;
    
    const roomName = `route:${routeId}`;
    this.io.to(roomName).emit(event, data);
    
    logger.debug(`Broadcasted ${event} to route ${routeId}`);
  }

  /**
   * Broadcast to all clients subscribed to a stop
   */
  broadcastToStop(stopId, event, data) {
    if (!this.io) return;
    
    const roomName = `stop:${stopId}`;
    this.io.to(roomName).emit(event, data);
    
    logger.debug(`Broadcasted ${event} to stop ${stopId}`);
  }

  /**
   * Broadcast emergency alert to all clients
   */
  broadcastEmergencyAlert(alertData) {
    if (!this.io) return;
    
    this.io.emit('emergency:alert:broadcast', alertData);
    
    logger.info(`Broadcasted emergency alert: ${alertData.alertId}`);
  }

  /**
   * Send current route status to a specific client
   */
  async sendRouteStatus(socket, routeId) {
    try {
      const Bus = require('../models/Bus');
      const buses = await Bus.find({ route: routeId, status: 'active' })
        .select('busNumber currentLocation currentPassengerCount capacity status');
      
      socket.emit('route:status', {
        routeId,
        buses: buses.map(bus => ({
          id: bus._id,
          busNumber: bus.busNumber,
          location: bus.currentLocation,
          passengerCount: bus.currentPassengerCount,
          capacity: bus.capacity,
          status: bus.status,
          occupancyPercentage: bus.occupancyPercentage
        }))
      });
    } catch (error) {
      logger.error('Error sending route status:', error);
    }
  }

  /**
   * Send current bus status to a specific client
   */
  async sendBusStatus(socket, busId) {
    try {
      const Bus = require('../models/Bus');
      const bus = await Bus.findById(busId)
        .populate('route', 'routeNumber routeName')
        .populate('currentStop', 'name')
        .populate('nextStop', 'name');
      
      if (bus) {
        socket.emit('bus:status', {
          id: bus._id,
          busNumber: bus.busNumber,
          route: bus.route,
          location: bus.currentLocation,
          currentStop: bus.currentStop,
          nextStop: bus.nextStop,
          passengerCount: bus.currentPassengerCount,
          capacity: bus.capacity,
          status: bus.status,
          estimatedArrival: bus.estimatedArrival
        });
      }
    } catch (error) {
      logger.error('Error sending bus status:', error);
    }
  }

  /**
   * Start connection monitoring
   */
  startConnectionMonitoring() {
    setInterval(() => {
      const now = new Date();
      const staleConnections = [];
      
      this.connectedClients.forEach((client, socketId) => {
        const timeSinceLastActivity = now - client.lastActivity;
        
        // Mark connections as stale if no activity for 5 minutes
        if (timeSinceLastActivity > 5 * 60 * 1000) {
          staleConnections.push(socketId);
        }
      });
      
      // Clean up stale connections
      staleConnections.forEach(socketId => {
        const client = this.connectedClients.get(socketId);
        if (client) {
          client.socket.disconnect(true);
          this.handleDisconnection(socketId);
        }
      });
      
      if (staleConnections.length > 0) {
        logger.info(`Cleaned up ${staleConnections.length} stale connections`);
      }
      
    }, 60000); // Check every minute
  }

  /**
   * Get connection statistics
   */
  getConnectionStats() {
    const stats = {
      totalConnections: this.connectedClients.size,
      subscriptionCounts: {},
      connectionsByAge: {
        under1min: 0,
        under5min: 0,
        under15min: 0,
        over15min: 0
      }
    };

    const now = new Date();
    
    this.connectedClients.forEach((client) => {
      // Count subscriptions
      client.subscriptions.forEach(subscription => {
        const type = subscription.split(':')[0];
        stats.subscriptionCounts[type] = (stats.subscriptionCounts[type] || 0) + 1;
      });

      // Count by connection age
      const connectionAge = now - client.connectedAt;
      const ageMinutes = connectionAge / (1000 * 60);
      
      if (ageMinutes < 1) stats.connectionsByAge.under1min++;
      else if (ageMinutes < 5) stats.connectionsByAge.under5min++;
      else if (ageMinutes < 15) stats.connectionsByAge.under15min++;
      else stats.connectionsByAge.over15min++;
    });

    return stats;
  }
}

module.exports = new RealTimeService();
