{"name": "government-bus-tracking-system", "version": "1.0.0", "description": "GPS-based tracking system for government buses with real-time passenger information", "main": "backend/src/server.js", "scripts": {"start": "node backend/src/server.js", "dev": "nodemon backend/src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint backend/src/**/*.js", "lint:fix": "eslint backend/src/**/*.js --fix", "build": "npm run build:backend && npm run build:web", "build:backend": "echo 'Backend build complete'", "build:web": "cd web-dashboard && npm run build", "setup": "npm install && cd mobile && npm install && cd ../web-dashboard && npm install", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "migrate": "node backend/scripts/migrate.js", "seed": "node backend/scripts/seed.js"}, "keywords": ["bus-tracking", "gps", "real-time", "government", "transportation", "passenger-information", "fleet-management"], "author": "Government Bus Tracking Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "socket.io": "^4.7.2", "redis": "^4.6.7", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "joi": "^17.9.2", "moment": "^2.29.4", "node-cron": "^3.0.2", "multer": "^1.4.5-lts.1", "compression": "^1.7.4", "rate-limiter-flexible": "^2.4.2", "winston": "^3.10.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.47.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.28.1", "@types/jest": "^29.5.4", "mongodb-memory-server": "^8.15.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/government/bus-tracking-system.git"}, "bugs": {"url": "https://github.com/government/bus-tracking-system/issues"}, "homepage": "https://github.com/government/bus-tracking-system#readme"}