/**
 * Calculate distance between two points using Haversine formula
 * @param {number} lat1 - Latitude of first point
 * @param {number} lon1 - Longitude of first point
 * @param {number} lat2 - Latitude of second point
 * @param {number} lon2 - Longitude of second point
 * @returns {number} Distance in meters
 */
const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = lat1 * Math.PI / 180; // φ, λ in radians
  const φ2 = lat2 * Math.PI / 180;
  const Δφ = (lat2 - lat1) * Math.PI / 180;
  const Δλ = (lon2 - lon1) * Math.PI / 180;

  const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) *
    Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
};

/**
 * Calculate bearing between two points
 * @param {number} lat1 - Latitude of first point
 * @param {number} lon1 - Longitude of first point
 * @param {number} lat2 - Latitude of second point
 * @param {number} lon2 - Longitude of second point
 * @returns {number} Bearing in degrees
 */
const calculateBearing = (lat1, lon1, lat2, lon2) => {
  const φ1 = lat1 * Math.PI / 180;
  const φ2 = lat2 * Math.PI / 180;
  const Δλ = (lon2 - lon1) * Math.PI / 180;

  const y = Math.sin(Δλ) * Math.cos(φ2);
  const x = Math.cos(φ1) * Math.sin(φ2) - Math.sin(φ1) * Math.cos(φ2) * Math.cos(Δλ);

  const θ = Math.atan2(y, x);

  return (θ * 180 / Math.PI + 360) % 360; // Bearing in degrees
};

/**
 * Calculate ETA based on distance and speed
 * @param {number} lat1 - Current latitude
 * @param {number} lon1 - Current longitude
 * @param {number} lat2 - Destination latitude
 * @param {number} lon2 - Destination longitude
 * @param {number} speed - Speed in km/h
 * @returns {Date} Estimated arrival time
 */
const calculateETA = (lat1, lon1, lat2, lon2, speed) => {
  const distance = calculateDistance(lat1, lon1, lat2, lon2); // meters
  const distanceKm = distance / 1000; // kilometers
  const timeHours = distanceKm / speed; // hours
  const timeMs = timeHours * 60 * 60 * 1000; // milliseconds

  return new Date(Date.now() + timeMs);
};

/**
 * Check if a point is within a polygon (geofencing)
 * @param {Array} point - [longitude, latitude]
 * @param {Array} polygon - Array of [longitude, latitude] points
 * @returns {boolean} True if point is inside polygon
 */
const isPointInPolygon = (point, polygon) => {
  const [x, y] = point;
  let inside = false;

  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const [xi, yi] = polygon[i];
    const [xj, yj] = polygon[j];

    if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
      inside = !inside;
    }
  }

  return inside;
};

/**
 * Check if a point is within a certain distance from a line (route)
 * @param {Array} point - [longitude, latitude]
 * @param {Array} lineStart - [longitude, latitude]
 * @param {Array} lineEnd - [longitude, latitude]
 * @param {number} threshold - Distance threshold in meters
 * @returns {boolean} True if point is within threshold distance
 */
const isPointNearLine = (point, lineStart, lineEnd, threshold) => {
  const distance = distanceToLineSegment(point, lineStart, lineEnd);
  return distance <= threshold;
};

/**
 * Calculate distance from a point to a line segment
 * @param {Array} point - [longitude, latitude]
 * @param {Array} lineStart - [longitude, latitude]
 * @param {Array} lineEnd - [longitude, latitude]
 * @returns {number} Distance in meters
 */
const distanceToLineSegment = (point, lineStart, lineEnd) => {
  const [px, py] = point;
  const [x1, y1] = lineStart;
  const [x2, y2] = lineEnd;

  const A = px - x1;
  const B = py - y1;
  const C = x2 - x1;
  const D = y2 - y1;

  const dot = A * C + B * D;
  const lenSq = C * C + D * D;
  let param = -1;

  if (lenSq !== 0) {
    param = dot / lenSq;
  }

  let xx, yy;

  if (param < 0) {
    xx = x1;
    yy = y1;
  } else if (param > 1) {
    xx = x2;
    yy = y2;
  } else {
    xx = x1 + param * C;
    yy = y1 + param * D;
  }

  return calculateDistance(py, px, yy, xx);
};

/**
 * Create a bounding box around a point
 * @param {number} lat - Latitude
 * @param {number} lon - Longitude
 * @param {number} radiusKm - Radius in kilometers
 * @returns {Object} Bounding box with min/max lat/lon
 */
const createBoundingBox = (lat, lon, radiusKm) => {
  const latRadian = lat * Math.PI / 180;
  const degLatKm = 110.54; // km per degree latitude
  const degLonKm = 110.54 * Math.cos(latRadian); // km per degree longitude

  const deltaLat = radiusKm / degLatKm;
  const deltaLon = radiusKm / degLonKm;

  return {
    minLat: lat - deltaLat,
    maxLat: lat + deltaLat,
    minLon: lon - deltaLon,
    maxLon: lon + deltaLon
  };
};

/**
 * Convert degrees to radians
 * @param {number} degrees
 * @returns {number} Radians
 */
const toRadians = (degrees) => {
  return degrees * Math.PI / 180;
};

/**
 * Convert radians to degrees
 * @param {number} radians
 * @returns {number} Degrees
 */
const toDegrees = (radians) => {
  return radians * 180 / Math.PI;
};

/**
 * Validate coordinates
 * @param {number} lat - Latitude
 * @param {number} lon - Longitude
 * @returns {boolean} True if coordinates are valid
 */
const isValidCoordinates = (lat, lon) => {
  return (
    typeof lat === 'number' &&
    typeof lon === 'number' &&
    lat >= -90 &&
    lat <= 90 &&
    lon >= -180 &&
    lon <= 180 &&
    !isNaN(lat) &&
    !isNaN(lon)
  );
};

/**
 * Format coordinates for display
 * @param {number} lat - Latitude
 * @param {number} lon - Longitude
 * @param {number} precision - Decimal places
 * @returns {string} Formatted coordinates
 */
const formatCoordinates = (lat, lon, precision = 6) => {
  return `${lat.toFixed(precision)}, ${lon.toFixed(precision)}`;
};

/**
 * Calculate the center point of multiple coordinates
 * @param {Array} coordinates - Array of [longitude, latitude] pairs
 * @returns {Array} Center point [longitude, latitude]
 */
const calculateCenter = (coordinates) => {
  if (!coordinates || coordinates.length === 0) {
    return [0, 0];
  }

  let totalLat = 0;
  let totalLon = 0;

  coordinates.forEach(([lon, lat]) => {
    totalLat += lat;
    totalLon += lon;
  });

  return [
    totalLon / coordinates.length,
    totalLat / coordinates.length
  ];
};

/**
 * Simplify a path by removing points that are too close together
 * @param {Array} path - Array of [longitude, latitude] pairs
 * @param {number} tolerance - Minimum distance between points in meters
 * @returns {Array} Simplified path
 */
const simplifyPath = (path, tolerance = 10) => {
  if (path.length <= 2) return path;

  const simplified = [path[0]];
  
  for (let i = 1; i < path.length - 1; i++) {
    const prev = path[i - 1];
    const current = path[i];
    const distance = calculateDistance(prev[1], prev[0], current[1], current[0]);
    
    if (distance >= tolerance) {
      simplified.push(current);
    }
  }
  
  simplified.push(path[path.length - 1]);
  return simplified;
};

module.exports = {
  calculateDistance,
  calculateBearing,
  calculateETA,
  isPointInPolygon,
  isPointNearLine,
  distanceToLineSegment,
  createBoundingBox,
  toRadians,
  toDegrees,
  isValidCoordinates,
  formatCoordinates,
  calculateCenter,
  simplifyPath
};
