console.log('🚀 Node.js is working!');
console.log('📅 Current time:', new Date().toISOString());
console.log('🔧 Node.js version:', process.version);
console.log('💻 Platform:', process.platform);
console.log('📁 Current directory:', process.cwd());

// Test basic HTTP server
const http = require('http');

const server = http.createServer((req, res) => {
  res.writeHead(200, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({
    message: 'Bus Tracking System - Basic Server Test',
    timestamp: new Date().toISOString(),
    url: req.url,
    method: req.method
  }));
});

const PORT = 3000;
server.listen(PORT, () => {
  console.log(`✅ Basic HTTP server running on http://localhost:${PORT}`);
  console.log('🌐 Try opening: http://localhost:3000 in your browser');
});

server.on('error', (err) => {
  console.error('❌ Server error:', err.message);
  if (err.code === 'EADDRINUSE') {
    console.log('🔄 Port 3000 is already in use. Trying port 3001...');
    server.listen(3001, () => {
      console.log(`✅ Basic HTTP server running on http://localhost:3001`);
    });
  }
});
