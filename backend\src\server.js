const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('rate-limiter-flexible');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

// Import routes
const authRoutes = require('./routes/auth');
const busRoutes = require('./routes/buses');
const routeRoutes = require('./routes/routes');
const stopRoutes = require('./routes/stops');
const trackingRoutes = require('./routes/tracking');
const userRoutes = require('./routes/users');

// Import middleware
const errorHandler = require('./middleware/errorHandler');
const logger = require('./utils/logger');

// Import services
const GPSTrackingService = require('./services/GPSTrackingService');
const NotificationService = require('./services/NotificationService');
const RealTimeService = require('./services/RealTimeService');

const app = express();
const server = createServer(app);

// Initialize Socket.IO
const io = new Server(server, {
  cors: {
    origin: process.env.SOCKET_IO_CORS_ORIGIN || "http://localhost:3001",
    methods: ["GET", "POST"]
  }
});

// Rate limiting
const rateLimiter = new rateLimit.RateLimiterMemory({
  keyGenerator: (req) => req.ip,
  points: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  duration: parseInt(process.env.RATE_LIMIT_WINDOW_MS) / 1000 || 900, // 15 minutes
});

const rateLimiterMiddleware = async (req, res, next) => {
  try {
    await rateLimiter.consume(req.ip);
    next();
  } catch (rejRes) {
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    res.set('Retry-After', String(secs));
    res.status(429).json({
      error: 'Too Many Requests',
      message: 'Rate limit exceeded. Please try again later.',
      retryAfter: secs
    });
  }
};

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "wss:", "ws:"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

// CORS configuration
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3001',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// General middleware
app.use(compression());
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Apply rate limiting to all routes
app.use(rateLimiterMiddleware);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0'
  });
});

// API routes
const apiRouter = express.Router();

apiRouter.use('/auth', authRoutes);
apiRouter.use('/buses', busRoutes);
apiRouter.use('/routes', routeRoutes);
apiRouter.use('/stops', stopRoutes);
apiRouter.use('/tracking', trackingRoutes);
apiRouter.use('/users', userRoutes);

app.use(`/api/${process.env.API_VERSION || 'v1'}`, apiRouter);

// API documentation (in development)
if (process.env.NODE_ENV === 'development' && process.env.ENABLE_API_DOCS === 'true') {
  const swaggerJsdoc = require('swagger-jsdoc');
  const swaggerUi = require('swagger-ui-express');
  
  const options = {
    definition: {
      openapi: '3.0.0',
      info: {
        title: 'Government Bus Tracking API',
        version: '1.0.0',
        description: 'API for GPS-based bus tracking system with real-time passenger information',
      },
      servers: [
        {
          url: `http://localhost:${process.env.PORT || 3000}/api/${process.env.API_VERSION || 'v1'}`,
          description: 'Development server',
        },
      ],
    },
    apis: ['./src/routes/*.js'], // paths to files containing OpenAPI definitions
  };
  
  const specs = swaggerJsdoc(options);
  app.use('/api/docs', swaggerUi.serve, swaggerUi.setup(specs));
}

// Socket.IO connection handling
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);
  
  // Handle bus location updates
  socket.on('bus:location:update', async (data) => {
    try {
      await GPSTrackingService.processLocationUpdate(data);
      // Broadcast to all clients subscribed to this bus/route
      socket.broadcast.emit('bus:location:updated', data);
    } catch (error) {
      logger.error('Error processing location update:', error);
      socket.emit('error', { message: 'Failed to process location update' });
    }
  });
  
  // Handle passenger count updates
  socket.on('bus:passengers:update', async (data) => {
    try {
      await GPSTrackingService.updatePassengerCount(data.busId, data.count);
      socket.broadcast.emit('bus:passengers:updated', data);
    } catch (error) {
      logger.error('Error updating passenger count:', error);
      socket.emit('error', { message: 'Failed to update passenger count' });
    }
  });
  
  // Handle emergency alerts
  socket.on('emergency:alert', async (data) => {
    try {
      await NotificationService.sendEmergencyAlert(data);
      io.emit('emergency:alert:broadcast', data);
    } catch (error) {
      logger.error('Error sending emergency alert:', error);
    }
  });
  
  // Handle route subscriptions
  socket.on('subscribe:route', (routeId) => {
    socket.join(`route:${routeId}`);
    logger.info(`Client ${socket.id} subscribed to route ${routeId}`);
  });
  
  socket.on('unsubscribe:route', (routeId) => {
    socket.leave(`route:${routeId}`);
    logger.info(`Client ${socket.id} unsubscribed from route ${routeId}`);
  });
  
  // Handle bus subscriptions
  socket.on('subscribe:bus', (busId) => {
    socket.join(`bus:${busId}`);
    logger.info(`Client ${socket.id} subscribed to bus ${busId}`);
  });
  
  socket.on('unsubscribe:bus', (busId) => {
    socket.leave(`bus:${busId}`);
    logger.info(`Client ${socket.id} unsubscribed from bus ${busId}`);
  });
  
  socket.on('disconnect', () => {
    logger.info(`Client disconnected: ${socket.id}`);
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: 'The requested resource was not found',
    path: req.originalUrl
  });
});

// Database connection
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    logger.info(`MongoDB Connected: ${conn.connection.host}`);
    
    // Initialize services after DB connection
    await GPSTrackingService.initialize();
    await NotificationService.initialize();
    RealTimeService.initialize(io);
    
  } catch (error) {
    logger.error('Database connection error:', error);
    process.exit(1);
  }
};

// Graceful shutdown
const gracefulShutdown = () => {
  logger.info('Received shutdown signal, closing server gracefully...');
  
  server.close(() => {
    logger.info('HTTP server closed');
    
    mongoose.connection.close(false, () => {
      logger.info('MongoDB connection closed');
      process.exit(0);
    });
  });
  
  // Force close after 30 seconds
  setTimeout(() => {
    logger.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 30000);
};

// Handle shutdown signals
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start server
const PORT = process.env.PORT || 3000;

const startServer = async () => {
  await connectDB();
  
  server.listen(PORT, () => {
    logger.info(`Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
    
    if (process.env.NODE_ENV === 'development') {
      logger.info(`API Documentation: http://localhost:${PORT}/api/docs`);
      logger.info(`Health Check: http://localhost:${PORT}/health`);
    }
  });
};

startServer().catch(error => {
  logger.error('Failed to start server:', error);
  process.exit(1);
});

module.exports = { app, server, io };
