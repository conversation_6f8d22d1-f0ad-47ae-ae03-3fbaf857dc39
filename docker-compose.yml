version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:5.0
    container_name: bus_tracking_mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-admin123}
      MONGO_INITDB_DATABASE: bus_tracking
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./docker/mongodb/init:/docker-entrypoint-initdb.d
    networks:
      - bus_tracking_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: bus_tracking_redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis123}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - bus_tracking_network

  # Backend API Server
  backend:
    build:
      context: .
      dockerfile: docker/backend/Dockerfile
    container_name: bus_tracking_backend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: 3000
      MONGODB_URI: mongodb://admin:${MONGO_ROOT_PASSWORD:-admin123}@mongodb:27017/bus_tracking?authSource=admin
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis123}
      JWT_SECRET: ${JWT_SECRET:-your_super_secret_jwt_key_here}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN:-7d}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY:-your_32_character_encryption_key}
      GOOGLE_MAPS_API_KEY: ${GOOGLE_MAPS_API_KEY}
      FCM_SERVER_KEY: ${FCM_SERVER_KEY}
      EMAIL_HOST: ${EMAIL_HOST}
      EMAIL_PORT: ${EMAIL_PORT:-587}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASS: ${EMAIL_PASS}
      SMS_API_KEY: ${SMS_API_KEY}
      SMS_API_SECRET: ${SMS_API_SECRET}
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost:3001}
      SOCKET_IO_CORS_ORIGIN: ${SOCKET_IO_CORS_ORIGIN:-http://localhost:3001}
      LOG_LEVEL: ${LOG_LEVEL:-info}
      RATE_LIMIT_MAX_REQUESTS: ${RATE_LIMIT_MAX_REQUESTS:-100}
      RATE_LIMIT_WINDOW_MS: ${RATE_LIMIT_WINDOW_MS:-900000}
    ports:
      - "3000:3000"
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    depends_on:
      - mongodb
      - redis
    networks:
      - bus_tracking_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Web Dashboard
  web_dashboard:
    build:
      context: ./web-dashboard
      dockerfile: ../docker/web-dashboard/Dockerfile
      args:
        REACT_APP_API_URL: ${REACT_APP_API_URL:-http://localhost:3000/api/v1}
        REACT_APP_SOCKET_URL: ${REACT_APP_SOCKET_URL:-http://localhost:3000}
        REACT_APP_GOOGLE_MAPS_API_KEY: ${GOOGLE_MAPS_API_KEY}
    container_name: bus_tracking_web_dashboard
    restart: unless-stopped
    ports:
      - "3001:80"
    depends_on:
      - backend
    networks:
      - bus_tracking_network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: bus_tracking_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./docker/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - backend
      - web_dashboard
    networks:
      - bus_tracking_network

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: bus_tracking_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - bus_tracking_network

  # Grafana for Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: bus_tracking_grafana
    restart: unless-stopped
    ports:
      - "3002:3000"
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER:-admin}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin123}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
      - ./docker/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
    networks:
      - bus_tracking_network

  # ElasticSearch for Logging
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: bus_tracking_elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - bus_tracking_network

  # Kibana for Log Visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: bus_tracking_kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - bus_tracking_network

  # Logstash for Log Processing
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: bus_tracking_logstash
    restart: unless-stopped
    volumes:
      - ./docker/logstash/pipeline:/usr/share/logstash/pipeline
      - ./logs:/usr/share/logstash/logs
    depends_on:
      - elasticsearch
    networks:
      - bus_tracking_network

  # Backup Service
  backup:
    build:
      context: ./docker/backup
      dockerfile: Dockerfile
    container_name: bus_tracking_backup
    restart: unless-stopped
    environment:
      MONGODB_URI: mongodb://admin:${MONGO_ROOT_PASSWORD:-admin123}@mongodb:27017/bus_tracking?authSource=admin
      BACKUP_SCHEDULE: ${BACKUP_SCHEDULE:-0 2 * * *}
      BACKUP_RETENTION_DAYS: ${BACKUP_RETENTION_DAYS:-30}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_S3_BUCKET: ${AWS_S3_BUCKET}
    volumes:
      - ./backups:/app/backups
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - mongodb
    networks:
      - bus_tracking_network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  bus_tracking_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
