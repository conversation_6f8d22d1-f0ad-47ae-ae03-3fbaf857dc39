const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  phoneNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  profile: {
    firstName: {
      type: String,
      required: true,
      trim: true
    },
    lastName: {
      type: String,
      required: true,
      trim: true
    },
    dateOfBirth: {
      type: Date
    },
    gender: {
      type: String,
      enum: ['male', 'female', 'other', 'prefer_not_to_say']
    },
    profilePicture: {
      type: String
    }
  },
  role: {
    type: String,
    enum: ['passenger', 'driver', 'conductor', 'admin', 'operator'],
    default: 'passenger'
  },
  preferences: {
    notifications: {
      busArrival: { type: Boolean, default: true },
      routeUpdates: { type: Boolean, default: true },
      emergencyAlerts: { type: Boolean, default: true },
      promotions: { type: Boolean, default: false }
    },
    language: {
      type: String,
      default: 'en',
      enum: ['en', 'es', 'fr', 'hi', 'ta', 'te']
    },
    theme: {
      type: String,
      default: 'light',
      enum: ['light', 'dark', 'auto']
    },
    defaultLocation: {
      name: String,
      coordinates: [Number]
    }
  },
  favoriteRoutes: [{
    route: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Route'
    },
    nickname: String,
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  favoriteStops: [{
    stop: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'BusStop'
    },
    nickname: String,
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  travelHistory: [{
    route: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Route'
    },
    bus: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Bus'
    },
    startStop: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'BusStop'
    },
    endStop: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'BusStop'
    },
    boardingTime: Date,
    alightingTime: Date,
    fare: Number,
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    feedback: String,
    date: {
      type: Date,
      default: Date.now
    }
  }],
  deviceTokens: [{
    token: {
      type: String,
      required: true
    },
    platform: {
      type: String,
      enum: ['ios', 'android', 'web'],
      required: true
    },
    lastUsed: {
      type: Date,
      default: Date.now
    }
  }],
  emergencyContacts: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    phoneNumber: {
      type: String,
      required: true,
      trim: true
    },
    relationship: {
      type: String,
      required: true,
      trim: true
    }
  }],
  accessibilityNeeds: {
    wheelchairUser: { type: Boolean, default: false },
    visualImpairment: { type: Boolean, default: false },
    hearingImpairment: { type: Boolean, default: false },
    mobilityAid: { type: Boolean, default: false },
    other: String
  },
  subscription: {
    type: {
      type: String,
      enum: ['free', 'premium', 'student', 'senior'],
      default: 'free'
    },
    startDate: Date,
    endDate: Date,
    autoRenew: { type: Boolean, default: false }
  },
  verification: {
    email: {
      verified: { type: Boolean, default: false },
      token: String,
      expiresAt: Date
    },
    phone: {
      verified: { type: Boolean, default: false },
      otp: String,
      expiresAt: Date
    }
  },
  security: {
    lastLogin: Date,
    loginAttempts: { type: Number, default: 0 },
    lockUntil: Date,
    passwordResetToken: String,
    passwordResetExpires: Date,
    twoFactorEnabled: { type: Boolean, default: false },
    twoFactorSecret: String
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'deleted'],
    default: 'active'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { 
    virtuals: true,
    transform: function(doc, ret) {
      delete ret.password;
      delete ret.security.twoFactorSecret;
      delete ret.verification.email.token;
      delete ret.verification.phone.otp;
      return ret;
    }
  },
  toObject: { virtuals: true }
});

// Indexes
userSchema.index({ email: 1 });
userSchema.index({ phoneNumber: 1 });
userSchema.index({ role: 1, status: 1 });
userSchema.index({ 'security.lastLogin': -1 });

// Virtual for full name
userSchema.virtual('fullName').get(function() {
  return `${this.profile.firstName} ${this.profile.lastName}`;
});

// Virtual for account locked status
userSchema.virtual('isLocked').get(function() {
  return !!(this.security.lockUntil && this.security.lockUntil > Date.now());
});

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Pre-save middleware to update timestamps
userSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Method to increment login attempts
userSchema.methods.incLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.security.lockUntil && this.security.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { 'security.lockUntil': 1 },
      $set: { 'security.loginAttempts': 1 }
    });
  }
  
  const updates = { $inc: { 'security.loginAttempts': 1 } };
  
  // Lock account after 5 failed attempts for 2 hours
  if (this.security.loginAttempts + 1 >= 5 && !this.isLocked) {
    updates.$set = { 'security.lockUntil': Date.now() + 2 * 60 * 60 * 1000 };
  }
  
  return this.updateOne(updates);
};

// Method to reset login attempts
userSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { 
      'security.loginAttempts': 1,
      'security.lockUntil': 1
    },
    $set: { 'security.lastLogin': Date.now() }
  });
};

// Method to add favorite route
userSchema.methods.addFavoriteRoute = function(routeId, nickname) {
  const existing = this.favoriteRoutes.find(fav => fav.route.toString() === routeId.toString());
  if (existing) return this;
  
  this.favoriteRoutes.push({ route: routeId, nickname });
  return this.save();
};

// Method to add favorite stop
userSchema.methods.addFavoriteStop = function(stopId, nickname) {
  const existing = this.favoriteStops.find(fav => fav.stop.toString() === stopId.toString());
  if (existing) return this;
  
  this.favoriteStops.push({ stop: stopId, nickname });
  return this.save();
};

// Method to add device token
userSchema.methods.addDeviceToken = function(token, platform) {
  // Remove existing token if it exists
  this.deviceTokens = this.deviceTokens.filter(dt => dt.token !== token);
  
  // Add new token
  this.deviceTokens.push({ token, platform, lastUsed: new Date() });
  
  // Keep only last 5 tokens
  if (this.deviceTokens.length > 5) {
    this.deviceTokens = this.deviceTokens.slice(-5);
  }
  
  return this.save();
};

// Static method to find by email or phone
userSchema.statics.findByEmailOrPhone = function(identifier) {
  return this.findOne({
    $or: [
      { email: identifier.toLowerCase() },
      { phoneNumber: identifier }
    ]
  });
};

module.exports = mongoose.model('User', userSchema);
