const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { createServer } = require('http');
const { Server } = require('socket.io');

console.log('🚀 Starting Simple Bus Tracking Server...');

const app = express();
const server = createServer(app);

// Initialize Socket.IO
const io = new Server(server, {
  cors: {
    origin: "http://localhost:3001",
    methods: ["GET", "POST"]
  }
});

// Basic middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: 'development',
    version: '1.0.0',
    message: 'Bus Tracking System is running!'
  });
});

// API routes
app.get('/api/v1/buses', (req, res) => {
  res.json({
    success: true,
    message: 'Bus Tracking API is working!',
    data: {
      buses: [
        {
          id: '1',
          busNumber: 'BUS001',
          route: 'City Center to Airport',
          status: 'active',
          currentPassengerCount: 25,
          capacity: 50,
          location: {
            latitude: 37.7749,
            longitude: -122.4194,
            lastUpdated: new Date().toISOString()
          }
        },
        {
          id: '2',
          busNumber: 'BUS002',
          route: 'Downtown to University',
          status: 'active',
          currentPassengerCount: 18,
          capacity: 40,
          location: {
            latitude: 37.7849,
            longitude: -122.4094,
            lastUpdated: new Date().toISOString()
          }
        }
      ]
    }
  });
});

app.get('/api/v1/routes', (req, res) => {
  res.json({
    success: true,
    data: {
      routes: [
        {
          id: '1',
          routeNumber: 'R001',
          routeName: 'City Center to Airport',
          startPoint: 'City Center Terminal',
          endPoint: 'Airport Terminal A',
          status: 'active'
        },
        {
          id: '2',
          routeNumber: 'R002',
          routeName: 'Downtown to University',
          startPoint: 'Downtown Station',
          endPoint: 'University Campus',
          status: 'active'
        }
      ]
    }
  });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log(`📱 Client connected: ${socket.id}`);
  
  // Send welcome message
  socket.emit('welcome', {
    message: 'Connected to Bus Tracking System',
    timestamp: new Date().toISOString()
  });
  
  // Handle bus location updates
  socket.on('bus:location:update', (data) => {
    console.log('📍 Bus location update:', data);
    // Broadcast to all clients
    socket.broadcast.emit('bus:location:updated', {
      ...data,
      timestamp: new Date().toISOString()
    });
  });
  
  // Handle passenger count updates
  socket.on('bus:passengers:update', (data) => {
    console.log('👥 Passenger count update:', data);
    socket.broadcast.emit('bus:passengers:updated', {
      ...data,
      timestamp: new Date().toISOString()
    });
  });
  
  socket.on('disconnect', () => {
    console.log(`📱 Client disconnected: ${socket.id}`);
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: 'The requested resource was not found',
    path: req.originalUrl,
    availableEndpoints: [
      'GET /health',
      'GET /api/v1/buses',
      'GET /api/v1/routes'
    ]
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error('❌ Error:', err);
  res.status(500).json({
    error: 'Internal Server Error',
    message: 'Something went wrong!',
    timestamp: new Date().toISOString()
  });
});

// Start server
const PORT = process.env.PORT || 3000;

server.listen(PORT, () => {
  console.log('');
  console.log('✅ Bus Tracking System Started Successfully!');
  console.log('🌐 Server running on port:', PORT);
  console.log('📊 Health Check: http://localhost:' + PORT + '/health');
  console.log('🚌 Buses API: http://localhost:' + PORT + '/api/v1/buses');
  console.log('🛣️  Routes API: http://localhost:' + PORT + '/api/v1/routes');
  console.log('🔌 WebSocket: ws://localhost:' + PORT);
  console.log('');
  console.log('📱 Ready to accept connections!');
  console.log('Press Ctrl+C to stop the server');
  console.log('');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Received SIGTERM, shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 Received SIGINT, shutting down gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});
