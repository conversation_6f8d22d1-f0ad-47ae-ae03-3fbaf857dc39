const http = require('http');

console.log('🔍 Checking Bus Tracking System Status...\n');

// Function to make HTTP request
function checkEndpoint(host, port, path, name) {
  return new Promise((resolve) => {
    const options = {
      hostname: host,
      port: port,
      path: path,
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          console.log(`✅ ${name}: OK (Status: ${res.statusCode})`);
          console.log(`   Response: ${jsonData.message || jsonData.status || 'Success'}`);
          resolve(true);
        } catch (e) {
          console.log(`✅ ${name}: OK (Status: ${res.statusCode})`);
          console.log(`   Response: ${data.substring(0, 100)}...`);
          resolve(true);
        }
      });
    });

    req.on('error', (err) => {
      console.log(`❌ ${name}: Failed`);
      console.log(`   Error: ${err.message}`);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log(`⏰ ${name}: Timeout`);
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// Check multiple endpoints
async function checkAllEndpoints() {
  const endpoints = [
    { host: 'localhost', port: 3000, path: '/health', name: 'Health Check (Port 3000)' },
    { host: 'localhost', port: 3001, path: '/health', name: 'Health Check (Port 3001)' },
    { host: 'localhost', port: 3000, path: '/api/v1/buses', name: 'Buses API (Port 3000)' },
    { host: 'localhost', port: 3001, path: '/api/v1/buses', name: 'Buses API (Port 3001)' }
  ];

  let successCount = 0;
  
  for (const endpoint of endpoints) {
    const success = await checkEndpoint(endpoint.host, endpoint.port, endpoint.path, endpoint.name);
    if (success) successCount++;
    console.log(''); // Empty line for readability
  }

  console.log('📊 Summary:');
  console.log(`   ${successCount}/${endpoints.length} endpoints responding`);
  
  if (successCount > 0) {
    console.log('🎉 Bus Tracking System is partially or fully operational!');
    console.log('🌐 You can also open the demo page: demo.html');
  } else {
    console.log('⚠️  No endpoints are responding. The server might not be running.');
    console.log('💡 Try running: node simple-server.js');
  }
}

checkAllEndpoints();
