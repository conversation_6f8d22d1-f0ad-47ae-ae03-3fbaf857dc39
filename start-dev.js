#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Create necessary directories
const dirs = ['logs', 'uploads'];
dirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`Created directory: ${dir}`);
  }
});

// Set development environment
process.env.NODE_ENV = 'development';
process.env.PORT = process.env.PORT || '3000';

console.log('🚀 Starting Bus Tracking System in Development Mode...');
console.log('📍 Environment: development');
console.log('🔌 Port:', process.env.PORT);
console.log('📊 API Documentation will be available at: http://localhost:' + process.env.PORT + '/api/docs');
console.log('❤️  Health Check: http://localhost:' + process.env.PORT + '/health');
console.log('');

// Start the server
require('./backend/src/server.js');
