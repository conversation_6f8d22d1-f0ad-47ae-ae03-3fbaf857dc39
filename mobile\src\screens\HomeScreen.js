import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  PermissionsAndroid,
  Platform,
  TouchableOpacity,
  ScrollView,
  RefreshControl,
} from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON>ine, PROVIDER_GOOGLE } from 'react-native-maps';
import Geolocation from 'react-native-geolocation-service';
import { Card, Button, Chip, ActivityIndicator } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';

import { BusService } from '../services/BusService';
import { LocationService } from '../services/LocationService';
import { SocketService } from '../services/SocketService';
import { colors, spacing, typography } from '../styles/theme';
import BusMarker from '../components/BusMarker';
import StopMarker from '../components/StopMarker';
import LoadingOverlay from '../components/LoadingOverlay';

const HomeScreen = () => {
  const navigation = useNavigation();
  const mapRef = useRef(null);
  
  // State management
  const [userLocation, setUserLocation] = useState(null);
  const [nearbyBuses, setNearbyBuses] = useState([]);
  const [nearbyStops, setNearbyStops] = useState([]);
  const [selectedBus, setSelectedBus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [mapReady, setMapReady] = useState(false);
  const [locationPermission, setLocationPermission] = useState(false);

  // Map configuration
  const [mapRegion, setMapRegion] = useState({
    latitude: 37.78825,
    longitude: -122.4324,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  });

  useEffect(() => {
    initializeApp();
    setupSocketListeners();
    
    return () => {
      SocketService.disconnect();
    };
  }, []);

  const initializeApp = async () => {
    try {
      setLoading(true);
      
      // Request location permission
      const hasPermission = await requestLocationPermission();
      if (hasPermission) {
        await getCurrentLocation();
        await loadNearbyData();
      }
      
      // Connect to socket for real-time updates
      SocketService.connect();
      
    } catch (error) {
      console.error('Error initializing app:', error);
      Alert.alert('Error', 'Failed to initialize the app. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const requestLocationPermission = async () => {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Location Permission',
            message: 'Bus Tracking App needs access to your location to show nearby buses and stops.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );
        const hasPermission = granted === PermissionsAndroid.RESULTS.GRANTED;
        setLocationPermission(hasPermission);
        return hasPermission;
      } else {
        // iOS permission handling would go here
        setLocationPermission(true);
        return true;
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
      return false;
    }
  };

  const getCurrentLocation = () => {
    return new Promise((resolve, reject) => {
      Geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          const location = { latitude, longitude };
          
          setUserLocation(location);
          setMapRegion({
            latitude,
            longitude,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          });
          
          resolve(location);
        },
        (error) => {
          console.error('Error getting location:', error);
          Alert.alert('Location Error', 'Unable to get your current location.');
          reject(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 10000,
        }
      );
    });
  };

  const loadNearbyData = async () => {
    if (!userLocation) return;

    try {
      const [buses, stops] = await Promise.all([
        BusService.getNearbyBuses(userLocation.longitude, userLocation.latitude, 2000),
        BusService.getNearbyStops(userLocation.longitude, userLocation.latitude, 1000),
      ]);

      setNearbyBuses(buses);
      setNearbyStops(stops);
    } catch (error) {
      console.error('Error loading nearby data:', error);
      Alert.alert('Error', 'Failed to load nearby buses and stops.');
    }
  };

  const setupSocketListeners = () => {
    // Listen for bus location updates
    SocketService.on('bus:location:updated', (data) => {
      setNearbyBuses(prevBuses => 
        prevBuses.map(bus => 
          bus._id === data.busId 
            ? { ...bus, currentLocation: data.location, ...data }
            : bus
        )
      );
    });

    // Listen for passenger count updates
    SocketService.on('bus:passengers:updated', (data) => {
      setNearbyBuses(prevBuses => 
        prevBuses.map(bus => 
          bus._id === data.busId 
            ? { ...bus, currentPassengerCount: data.currentPassengerCount, ...data }
            : bus
        )
      );
    });

    // Listen for emergency alerts
    SocketService.on('emergency:alert:broadcast', (data) => {
      Alert.alert(
        'Emergency Alert',
        data.message,
        [{ text: 'OK', style: 'default' }],
        { cancelable: false }
      );
    });
  };

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      if (locationPermission) {
        await getCurrentLocation();
        await loadNearbyData();
      }
    } catch (error) {
      console.error('Error refreshing:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const onBusMarkerPress = (bus) => {
    setSelectedBus(bus);
    
    // Center map on selected bus
    if (mapRef.current && bus.currentLocation) {
      mapRef.current.animateToRegion({
        latitude: bus.currentLocation.coordinates[1],
        longitude: bus.currentLocation.coordinates[0],
        latitudeDelta: 0.005,
        longitudeDelta: 0.005,
      });
    }
  };

  const onStopMarkerPress = (stop) => {
    navigation.navigate('StopDetails', { stopId: stop._id });
  };

  const centerOnUserLocation = () => {
    if (userLocation && mapRef.current) {
      mapRef.current.animateToRegion({
        ...userLocation,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    }
  };

  const renderBusCard = () => {
    if (!selectedBus) return null;

    return (
      <Card style={styles.busCard}>
        <Card.Content>
          <View style={styles.busCardHeader}>
            <Text style={styles.busNumber}>{selectedBus.busNumber}</Text>
            <Chip 
              mode="outlined" 
              style={[styles.statusChip, { backgroundColor: getStatusColor(selectedBus.status) }]}
            >
              {selectedBus.status.toUpperCase()}
            </Chip>
          </View>
          
          <Text style={styles.routeInfo}>
            {selectedBus.route?.routeName || 'Unknown Route'}
          </Text>
          
          <View style={styles.busInfo}>
            <View style={styles.infoItem}>
              <Icon name="people" size={20} color={colors.primary} />
              <Text style={styles.infoText}>
                {selectedBus.currentPassengerCount}/{selectedBus.capacity}
              </Text>
            </View>
            
            <View style={styles.infoItem}>
              <Icon name="event-seat" size={20} color={colors.primary} />
              <Text style={styles.infoText}>
                {selectedBus.capacity - selectedBus.currentPassengerCount} available
              </Text>
            </View>
          </View>
          
          <View style={styles.cardActions}>
            <Button 
              mode="outlined" 
              onPress={() => navigation.navigate('BusDetails', { busId: selectedBus._id })}
              style={styles.actionButton}
            >
              View Details
            </Button>
            <Button 
              mode="contained" 
              onPress={() => navigation.navigate('RouteDetails', { routeId: selectedBus.route._id })}
              style={styles.actionButton}
            >
              View Route
            </Button>
          </View>
        </Card.Content>
      </Card>
    );
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return colors.success;
      case 'inactive': return colors.warning;
      case 'maintenance': return colors.info;
      case 'emergency': return colors.error;
      default: return colors.surface;
    }
  };

  if (loading) {
    return <LoadingOverlay message="Loading nearby buses..." />;
  }

  if (!locationPermission) {
    return (
      <View style={styles.permissionContainer}>
        <Icon name="location-off" size={64} color={colors.disabled} />
        <Text style={styles.permissionTitle}>Location Permission Required</Text>
        <Text style={styles.permissionText}>
          Please enable location access to view nearby buses and stops.
        </Text>
        <Button 
          mode="contained" 
          onPress={requestLocationPermission}
          style={styles.permissionButton}
        >
          Enable Location
        </Button>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <MapView
        ref={mapRef}
        provider={PROVIDER_GOOGLE}
        style={styles.map}
        region={mapRegion}
        onMapReady={() => setMapReady(true)}
        showsUserLocation={true}
        showsMyLocationButton={false}
        followsUserLocation={false}
        showsCompass={true}
        showsScale={true}
      >
        {/* User location marker */}
        {userLocation && (
          <Marker
            coordinate={userLocation}
            title="Your Location"
            pinColor={colors.primary}
          />
        )}

        {/* Bus markers */}
        {nearbyBuses.map((bus) => (
          <BusMarker
            key={bus._id}
            bus={bus}
            onPress={() => onBusMarkerPress(bus)}
            isSelected={selectedBus?._id === bus._id}
          />
        ))}

        {/* Stop markers */}
        {nearbyStops.map((stop) => (
          <StopMarker
            key={stop._id}
            stop={stop}
            onPress={() => onStopMarkerPress(stop)}
          />
        ))}
      </MapView>

      {/* Floating action button for user location */}
      <TouchableOpacity 
        style={styles.locationButton}
        onPress={centerOnUserLocation}
      >
        <Icon name="my-location" size={24} color={colors.onPrimary} />
      </TouchableOpacity>

      {/* Bus information card */}
      {selectedBus && renderBusCard()}

      {/* Refresh control */}
      <ScrollView
        style={styles.refreshContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
          />
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  map: {
    flex: 1,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.large,
    backgroundColor: colors.background,
  },
  permissionTitle: {
    ...typography.headlineSmall,
    marginTop: spacing.medium,
    marginBottom: spacing.small,
    textAlign: 'center',
  },
  permissionText: {
    ...typography.bodyMedium,
    textAlign: 'center',
    marginBottom: spacing.large,
    color: colors.onSurfaceVariant,
  },
  permissionButton: {
    marginTop: spacing.medium,
  },
  locationButton: {
    position: 'absolute',
    top: 60,
    right: spacing.medium,
    backgroundColor: colors.primary,
    borderRadius: 28,
    width: 56,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  busCard: {
    position: 'absolute',
    bottom: spacing.medium,
    left: spacing.medium,
    right: spacing.medium,
    elevation: 4,
  },
  busCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.small,
  },
  busNumber: {
    ...typography.headlineSmall,
    fontWeight: 'bold',
  },
  statusChip: {
    height: 32,
  },
  routeInfo: {
    ...typography.bodyLarge,
    color: colors.onSurfaceVariant,
    marginBottom: spacing.medium,
  },
  busInfo: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: spacing.medium,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoText: {
    ...typography.bodyMedium,
    marginLeft: spacing.small,
  },
  cardActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    marginHorizontal: spacing.small,
  },
  refreshContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 1,
  },
});

export default HomeScreen;
