const logger = require('../utils/logger');
const { calculateDistance, isPointInPolygon } = require('../utils/geoUtils');
const NotificationService = require('./NotificationService');
const RealTimeService = require('./RealTimeService');

class SecurityService {
  constructor() {
    this.geofences = new Map();
    this.speedLimits = new Map();
    this.emergencyProtocols = new Map();
    this.securityAlerts = [];
    this.monitoringInterval = null;
  }

  /**
   * Initialize security service
   */
  async initialize() {
    try {
      logger.info('Initializing Security Service...');
      
      // Load geofences from database
      await this.loadGeofences();
      
      // Load speed limits
      await this.loadSpeedLimits();
      
      // Load emergency protocols
      await this.loadEmergencyProtocols();
      
      // Start security monitoring
      this.startSecurityMonitoring();
      
      logger.info('Security Service initialized successfully');
    } catch (error) {
      logger.error('Error initializing Security Service:', error);
      throw error;
    }
  }

  /**
   * Load geofences from database
   */
  async loadGeofences() {
    try {
      const Route = require('../models/Route');
      const routes = await Route.find({ status: 'active' });
      
      routes.forEach(route => {
        // Create geofence buffer around route path
        const geofence = this.createRouteGeofence(route);
        this.geofences.set(route._id.toString(), geofence);
      });
      
      logger.info(`Loaded ${this.geofences.size} route geofences`);
    } catch (error) {
      logger.error('Error loading geofences:', error);
    }
  }

  /**
   * Load speed limits for routes
   */
  async loadSpeedLimits() {
    try {
      const Route = require('../models/Route');
      const routes = await Route.find({ status: 'active' });
      
      routes.forEach(route => {
        // Default speed limit or route-specific limit
        const speedLimit = route.speedLimit || 60; // 60 km/h default
        this.speedLimits.set(route._id.toString(), speedLimit);
      });
      
      logger.info(`Loaded speed limits for ${this.speedLimits.size} routes`);
    } catch (error) {
      logger.error('Error loading speed limits:', error);
    }
  }

  /**
   * Load emergency protocols
   */
  async loadEmergencyProtocols() {
    try {
      // Define emergency protocols
      const protocols = {
        'medical_emergency': {
          priority: 'critical',
          autoNotify: ['emergency_services', 'admin', 'route_operators'],
          actions: ['stop_bus', 'call_ambulance', 'notify_passengers'],
          responseTime: 300 // 5 minutes
        },
        'security_threat': {
          priority: 'critical',
          autoNotify: ['police', 'admin', 'security_team'],
          actions: ['alert_authorities', 'evacuate_if_safe', 'track_location'],
          responseTime: 180 // 3 minutes
        },
        'mechanical_failure': {
          priority: 'high',
          autoNotify: ['maintenance_team', 'route_operators'],
          actions: ['stop_bus', 'arrange_replacement', 'notify_passengers'],
          responseTime: 900 // 15 minutes
        },
        'route_deviation': {
          priority: 'medium',
          autoNotify: ['route_operators', 'driver'],
          actions: ['alert_driver', 'guide_back_to_route'],
          responseTime: 600 // 10 minutes
        },
        'speed_violation': {
          priority: 'medium',
          autoNotify: ['route_operators', 'driver'],
          actions: ['alert_driver', 'log_incident'],
          responseTime: 300 // 5 minutes
        },
        'panic_button': {
          priority: 'critical',
          autoNotify: ['emergency_services', 'police', 'admin'],
          actions: ['immediate_response', 'track_location', 'open_communication'],
          responseTime: 120 // 2 minutes
        }
      };

      protocols.forEach((protocol, type) => {
        this.emergencyProtocols.set(type, protocol);
      });
      
      logger.info(`Loaded ${this.emergencyProtocols.size} emergency protocols`);
    } catch (error) {
      logger.error('Error loading emergency protocols:', error);
    }
  }

  /**
   * Create geofence buffer around route path
   */
  createRouteGeofence(route) {
    try {
      const bufferDistance = 200; // 200 meters buffer
      const coordinates = route.routePath.coordinates;
      
      // Create a simplified polygon buffer around the route
      // In a real implementation, you'd use a proper GIS library
      const geofence = {
        routeId: route._id,
        type: 'route_buffer',
        coordinates: coordinates,
        buffer: bufferDistance,
        bounds: this.calculateRouteBounds(coordinates)
      };
      
      return geofence;
    } catch (error) {
      logger.error('Error creating route geofence:', error);
      return null;
    }
  }

  /**
   * Calculate bounding box for route coordinates
   */
  calculateRouteBounds(coordinates) {
    let minLat = Infinity, maxLat = -Infinity;
    let minLng = Infinity, maxLng = -Infinity;
    
    coordinates.forEach(([lng, lat]) => {
      minLat = Math.min(minLat, lat);
      maxLat = Math.max(maxLat, lat);
      minLng = Math.min(minLng, lng);
      maxLng = Math.max(maxLng, lng);
    });
    
    return { minLat, maxLat, minLng, maxLng };
  }

  /**
   * Check if bus is within geofence
   */
  checkGeofence(busLocation, routeId) {
    try {
      const geofence = this.geofences.get(routeId.toString());
      if (!geofence) return true; // No geofence defined, assume OK
      
      const [lng, lat] = busLocation.coordinates;
      
      // Quick bounds check first
      const bounds = geofence.bounds;
      if (lat < bounds.minLat || lat > bounds.maxLat || 
          lng < bounds.minLng || lng > bounds.maxLng) {
        return false;
      }
      
      // Check distance to route path
      let minDistance = Infinity;
      const routeCoords = geofence.coordinates;
      
      for (let i = 0; i < routeCoords.length - 1; i++) {
        const segmentStart = routeCoords[i];
        const segmentEnd = routeCoords[i + 1];
        
        const distance = this.distanceToLineSegment(
          [lng, lat], segmentStart, segmentEnd
        );
        
        minDistance = Math.min(minDistance, distance);
      }
      
      return minDistance <= geofence.buffer;
    } catch (error) {
      logger.error('Error checking geofence:', error);
      return true; // Default to true to avoid false alarms
    }
  }

  /**
   * Calculate distance from point to line segment
   */
  distanceToLineSegment(point, lineStart, lineEnd) {
    const [px, py] = point;
    const [x1, y1] = lineStart;
    const [x2, y2] = lineEnd;

    const A = px - x1;
    const B = py - y1;
    const C = x2 - x1;
    const D = y2 - y1;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    let param = -1;

    if (lenSq !== 0) {
      param = dot / lenSq;
    }

    let xx, yy;

    if (param < 0) {
      xx = x1;
      yy = y1;
    } else if (param > 1) {
      xx = x2;
      yy = y2;
    } else {
      xx = x1 + param * C;
      yy = y1 + param * D;
    }

    return calculateDistance(py, px, yy, xx);
  }

  /**
   * Check speed violation
   */
  checkSpeedViolation(speed, routeId) {
    try {
      const speedLimit = this.speedLimits.get(routeId.toString()) || 60;
      return speed > speedLimit;
    } catch (error) {
      logger.error('Error checking speed violation:', error);
      return false;
    }
  }

  /**
   * Process security alert
   */
  async processSecurityAlert(alertData) {
    try {
      const { busId, type, severity, location, message, userId } = alertData;
      
      // Get emergency protocol for this alert type
      const protocol = this.emergencyProtocols.get(type);
      if (!protocol) {
        logger.warn(`No emergency protocol found for alert type: ${type}`);
        return;
      }
      
      // Create security alert record
      const securityAlert = {
        id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        busId,
        type,
        severity: severity || protocol.priority,
        location,
        message,
        userId,
        timestamp: new Date(),
        status: 'active',
        protocol: protocol,
        responseDeadline: new Date(Date.now() + protocol.responseTime * 1000)
      };
      
      this.securityAlerts.push(securityAlert);
      
      // Execute emergency protocol
      await this.executeEmergencyProtocol(securityAlert);
      
      // Log the security alert
      logger.warn(`Security alert processed: ${type} for bus ${busId}`, securityAlert);
      
      return securityAlert;
    } catch (error) {
      logger.error('Error processing security alert:', error);
      throw error;
    }
  }

  /**
   * Execute emergency protocol
   */
  async executeEmergencyProtocol(alert) {
    try {
      const protocol = alert.protocol;
      
      // Send notifications based on protocol
      if (protocol.autoNotify.includes('emergency_services')) {
        await this.notifyEmergencyServices(alert);
      }
      
      if (protocol.autoNotify.includes('police')) {
        await this.notifyPolice(alert);
      }
      
      if (protocol.autoNotify.includes('admin')) {
        await this.notifyAdministrators(alert);
      }
      
      if (protocol.autoNotify.includes('route_operators')) {
        await this.notifyRouteOperators(alert);
      }
      
      if (protocol.autoNotify.includes('maintenance_team')) {
        await this.notifyMaintenanceTeam(alert);
      }
      
      // Execute automated actions
      for (const action of protocol.actions) {
        await this.executeAutomatedAction(action, alert);
      }
      
      // Broadcast alert to real-time clients
      RealTimeService.broadcastEmergencyAlert(alert);
      
      logger.info(`Emergency protocol executed for alert ${alert.id}`);
    } catch (error) {
      logger.error('Error executing emergency protocol:', error);
    }
  }

  /**
   * Execute automated action
   */
  async executeAutomatedAction(action, alert) {
    try {
      switch (action) {
        case 'stop_bus':
          await this.sendStopBusCommand(alert.busId);
          break;
          
        case 'call_ambulance':
          await this.callEmergencyServices('ambulance', alert);
          break;
          
        case 'alert_authorities':
          await this.alertAuthorities(alert);
          break;
          
        case 'track_location':
          await this.enableEnhancedTracking(alert.busId);
          break;
          
        case 'notify_passengers':
          await this.notifyPassengers(alert);
          break;
          
        case 'alert_driver':
          await this.alertDriver(alert.busId, alert.message);
          break;
          
        case 'immediate_response':
          await this.triggerImmediateResponse(alert);
          break;
          
        case 'open_communication':
          await this.openEmergencyCommunication(alert.busId);
          break;
          
        default:
          logger.warn(`Unknown automated action: ${action}`);
      }
    } catch (error) {
      logger.error(`Error executing automated action ${action}:`, error);
    }
  }

  /**
   * Send stop bus command
   */
  async sendStopBusCommand(busId) {
    try {
      // Send command to bus system to stop
      RealTimeService.broadcastToBus(busId, 'emergency:stop_command', {
        command: 'stop',
        reason: 'emergency',
        timestamp: new Date()
      });
      
      logger.info(`Stop command sent to bus ${busId}`);
    } catch (error) {
      logger.error('Error sending stop bus command:', error);
    }
  }

  /**
   * Enable enhanced tracking for a bus
   */
  async enableEnhancedTracking(busId) {
    try {
      // Increase GPS update frequency
      RealTimeService.broadcastToBus(busId, 'tracking:enhance', {
        updateInterval: 5000, // 5 seconds
        highAccuracy: true,
        duration: 3600000 // 1 hour
      });
      
      logger.info(`Enhanced tracking enabled for bus ${busId}`);
    } catch (error) {
      logger.error('Error enabling enhanced tracking:', error);
    }
  }

  /**
   * Alert driver
   */
  async alertDriver(busId, message) {
    try {
      RealTimeService.broadcastToBus(busId, 'driver:alert', {
        message,
        priority: 'high',
        requiresAcknowledgment: true,
        timestamp: new Date()
      });
      
      logger.info(`Driver alert sent to bus ${busId}`);
    } catch (error) {
      logger.error('Error alerting driver:', error);
    }
  }

  /**
   * Notify emergency services
   */
  async notifyEmergencyServices(alert) {
    try {
      const message = {
        title: 'Bus Emergency Alert',
        body: `Emergency on bus ${alert.busId}: ${alert.message}`,
        data: {
          type: 'emergency',
          alertId: alert.id,
          busId: alert.busId,
          location: JSON.stringify(alert.location),
          severity: alert.severity
        }
      };
      
      // This would integrate with emergency services API
      logger.info(`Emergency services notified for alert ${alert.id}`);
    } catch (error) {
      logger.error('Error notifying emergency services:', error);
    }
  }

  /**
   * Notify administrators
   */
  async notifyAdministrators(alert) {
    try {
      await NotificationService.sendEmergencyAlert({
        busId: alert.busId,
        type: alert.type,
        message: alert.message,
        severity: alert.severity,
        location: alert.location
      });
    } catch (error) {
      logger.error('Error notifying administrators:', error);
    }
  }

  /**
   * Start security monitoring
   */
  startSecurityMonitoring() {
    // Monitor security alerts every 30 seconds
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.checkActiveAlerts();
        await this.monitorBusLocations();
        await this.checkResponseDeadlines();
      } catch (error) {
        logger.error('Error in security monitoring:', error);
      }
    }, 30000);
    
    logger.info('Security monitoring started');
  }

  /**
   * Check active alerts
   */
  async checkActiveAlerts() {
    try {
      const activeAlerts = this.securityAlerts.filter(alert => alert.status === 'active');
      
      for (const alert of activeAlerts) {
        // Check if response deadline has passed
        if (new Date() > alert.responseDeadline) {
          await this.escalateAlert(alert);
        }
      }
    } catch (error) {
      logger.error('Error checking active alerts:', error);
    }
  }

  /**
   * Monitor bus locations for security violations
   */
  async monitorBusLocations() {
    try {
      const Bus = require('../models/Bus');
      const activeBuses = await Bus.find({ status: 'active', isOnRoute: true });
      
      for (const bus of activeBuses) {
        // Check geofence violations
        if (!this.checkGeofence(bus.currentLocation, bus.route)) {
          await this.processSecurityAlert({
            busId: bus._id,
            type: 'route_deviation',
            severity: 'medium',
            location: bus.currentLocation,
            message: `Bus ${bus.busNumber} has deviated from route`
          });
        }
        
        // Check speed violations
        if (this.checkSpeedViolation(bus.currentLocation.speed || 0, bus.route)) {
          await this.processSecurityAlert({
            busId: bus._id,
            type: 'speed_violation',
            severity: 'medium',
            location: bus.currentLocation,
            message: `Bus ${bus.busNumber} exceeding speed limit: ${bus.currentLocation.speed} km/h`
          });
        }
      }
    } catch (error) {
      logger.error('Error monitoring bus locations:', error);
    }
  }

  /**
   * Check response deadlines
   */
  async checkResponseDeadlines() {
    try {
      const now = new Date();
      const overdueAlerts = this.securityAlerts.filter(
        alert => alert.status === 'active' && now > alert.responseDeadline
      );
      
      for (const alert of overdueAlerts) {
        await this.escalateAlert(alert);
      }
    } catch (error) {
      logger.error('Error checking response deadlines:', error);
    }
  }

  /**
   * Escalate alert
   */
  async escalateAlert(alert) {
    try {
      alert.status = 'escalated';
      alert.escalatedAt = new Date();
      
      // Notify higher authorities
      logger.warn(`Alert escalated: ${alert.id} - ${alert.message}`);
      
      // Send escalation notifications
      await NotificationService.sendEmergencyAlert({
        ...alert,
        message: `ESCALATED: ${alert.message}`,
        severity: 'critical'
      });
    } catch (error) {
      logger.error('Error escalating alert:', error);
    }
  }

  /**
   * Get security statistics
   */
  getSecurityStats() {
    try {
      const now = new Date();
      const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      
      const recentAlerts = this.securityAlerts.filter(
        alert => alert.timestamp > last24Hours
      );
      
      const stats = {
        totalAlerts: this.securityAlerts.length,
        recentAlerts: recentAlerts.length,
        activeAlerts: this.securityAlerts.filter(alert => alert.status === 'active').length,
        escalatedAlerts: this.securityAlerts.filter(alert => alert.status === 'escalated').length,
        alertsByType: {},
        geofencesActive: this.geofences.size,
        speedLimitsActive: this.speedLimits.size
      };
      
      // Count alerts by type
      recentAlerts.forEach(alert => {
        stats.alertsByType[alert.type] = (stats.alertsByType[alert.type] || 0) + 1;
      });
      
      return stats;
    } catch (error) {
      logger.error('Error getting security stats:', error);
      return null;
    }
  }

  /**
   * Acknowledge security alert
   */
  acknowledgeAlert(alertId, userId) {
    try {
      const alert = this.securityAlerts.find(alert => alert.id === alertId);
      if (alert) {
        alert.status = 'acknowledged';
        alert.acknowledgedBy = userId;
        alert.acknowledgedAt = new Date();
        
        logger.info(`Alert acknowledged: ${alertId} by user ${userId}`);
        return true;
      }
      return false;
    } catch (error) {
      logger.error('Error acknowledging alert:', error);
      return false;
    }
  }
}

module.exports = new SecurityService();
