<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bus Tracking System - Live Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        
        .status-online {
            background: #27ae60;
        }
        
        .status-offline {
            background: #e74c3c;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .content {
            padding: 40px;
        }
        
        .api-section {
            margin-bottom: 30px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }
        
        .api-section h2 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .api-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: background 0.3s;
        }
        
        .api-button:hover {
            background: #2980b9;
        }
        
        .api-response {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
            display: none;
        }
        
        .bus-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .bus-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .bus-card:hover {
            transform: translateY(-5px);
        }
        
        .bus-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 10px;
        }
        
        .bus-route {
            color: #7f8c8d;
            margin-bottom: 15px;
        }
        
        .bus-status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            background: #d5f4e6;
            color: #27ae60;
            margin-bottom: 15px;
        }
        
        .passenger-info {
            display: flex;
            justify-content: space-between;
            padding-top: 15px;
            border-top: 1px solid #ecf0f1;
        }
        
        .websocket-section {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .log-area {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚌 Bus Tracking System - Live Demo</h1>
            <p>Real-time API Testing Interface</p>
            <div style="margin-top: 15px;">
                <span id="statusIndicator" class="status-indicator status-offline"></span>
                <span id="statusText">Checking connection...</span>
            </div>
        </div>
        
        <div class="content">
            <div class="api-section">
                <h2>🔍 API Testing</h2>
                <button class="api-button" onclick="testHealthCheck()">Health Check</button>
                <button class="api-button" onclick="testBusesAPI()">Get Buses</button>
                <button class="api-button" onclick="testRoutesAPI()">Get Routes</button>
                <button class="api-button" onclick="clearResponse()">Clear</button>
                <div id="apiResponse" class="api-response"></div>
            </div>
            
            <div class="api-section">
                <h2>🚌 Live Bus Data</h2>
                <button class="api-button" onclick="loadBusData()">Load Bus Data</button>
                <button class="api-button" onclick="startAutoRefresh()">Auto Refresh (30s)</button>
                <button class="api-button" onclick="stopAutoRefresh()">Stop Auto Refresh</button>
                <div id="busGrid" class="bus-grid"></div>
            </div>
            
            <div class="websocket-section">
                <h2>🔌 WebSocket Connection</h2>
                <button class="api-button" onclick="connectWebSocket()">Connect WebSocket</button>
                <button class="api-button" onclick="disconnectWebSocket()">Disconnect</button>
                <button class="api-button" onclick="sendTestMessage()">Send Test Message</button>
                <div id="wsLog" class="log-area"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script>
        let socket = null;
        let autoRefreshInterval = null;
        
        // Check server status on load
        window.onload = function() {
            checkServerStatus();
            loadBusData();
        };
        
        async function checkServerStatus() {
            try {
                const response = await fetch('http://localhost:3000/health');
                if (response.ok) {
                    document.getElementById('statusIndicator').className = 'status-indicator status-online';
                    document.getElementById('statusText').textContent = 'Server Online';
                } else {
                    throw new Error('Server not responding');
                }
            } catch (error) {
                document.getElementById('statusIndicator').className = 'status-indicator status-offline';
                document.getElementById('statusText').textContent = 'Server Offline';
            }
        }
        
        async function testHealthCheck() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const data = await response.json();
                showResponse('Health Check Response:', data);
            } catch (error) {
                showResponse('Health Check Error:', { error: error.message });
            }
        }
        
        async function testBusesAPI() {
            try {
                const response = await fetch('http://localhost:3000/api/v1/buses');
                const data = await response.json();
                showResponse('Buses API Response:', data);
            } catch (error) {
                showResponse('Buses API Error:', { error: error.message });
            }
        }
        
        async function testRoutesAPI() {
            try {
                const response = await fetch('http://localhost:3000/api/v1/routes');
                const data = await response.json();
                showResponse('Routes API Response:', data);
            } catch (error) {
                showResponse('Routes API Error:', { error: error.message });
            }
        }
        
        function showResponse(title, data) {
            const responseDiv = document.getElementById('apiResponse');
            responseDiv.style.display = 'block';
            responseDiv.textContent = title + '\n' + JSON.stringify(data, null, 2);
        }
        
        function clearResponse() {
            const responseDiv = document.getElementById('apiResponse');
            responseDiv.style.display = 'none';
            responseDiv.textContent = '';
        }
        
        async function loadBusData() {
            try {
                const response = await fetch('http://localhost:3000/api/v1/buses');
                const data = await response.json();
                
                if (data.success && data.data.buses) {
                    displayBuses(data.data.buses);
                }
            } catch (error) {
                console.error('Error loading bus data:', error);
            }
        }
        
        function displayBuses(buses) {
            const busGrid = document.getElementById('busGrid');
            busGrid.innerHTML = '';
            
            buses.forEach(bus => {
                const busCard = document.createElement('div');
                busCard.className = 'bus-card';
                busCard.innerHTML = `
                    <div class="bus-number">${bus.busNumber}</div>
                    <div class="bus-route">${bus.route}</div>
                    <div class="bus-status">${bus.status.toUpperCase()}</div>
                    <div>📍 Location: ${bus.location.latitude.toFixed(4)}, ${bus.location.longitude.toFixed(4)}</div>
                    <div>🕒 Last Updated: ${new Date(bus.location.lastUpdated).toLocaleTimeString()}</div>
                    <div class="passenger-info">
                        <span>👥 ${bus.currentPassengerCount}/${bus.capacity}</span>
                        <span>🪑 ${bus.capacity - bus.currentPassengerCount} available</span>
                    </div>
                `;
                busGrid.appendChild(busCard);
            });
        }
        
        function startAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
            
            autoRefreshInterval = setInterval(() => {
                loadBusData();
                logMessage('🔄 Auto-refreshed bus data');
            }, 30000);
            
            logMessage('✅ Auto-refresh started (30 seconds interval)');
        }
        
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                logMessage('⏹️ Auto-refresh stopped');
            }
        }
        
        function connectWebSocket() {
            if (socket) {
                socket.disconnect();
            }
            
            socket = io('http://localhost:3000');
            
            socket.on('connect', () => {
                logMessage('🔌 WebSocket connected: ' + socket.id);
            });
            
            socket.on('welcome', (data) => {
                logMessage('👋 Welcome message: ' + data.message);
            });
            
            socket.on('bus:location:updated', (data) => {
                logMessage('📍 Bus location updated: ' + JSON.stringify(data));
            });
            
            socket.on('bus:passengers:updated', (data) => {
                logMessage('👥 Passenger count updated: ' + JSON.stringify(data));
            });
            
            socket.on('disconnect', () => {
                logMessage('❌ WebSocket disconnected');
            });
            
            socket.on('error', (error) => {
                logMessage('⚠️ WebSocket error: ' + error);
            });
        }
        
        function disconnectWebSocket() {
            if (socket) {
                socket.disconnect();
                socket = null;
                logMessage('🔌 WebSocket disconnected manually');
            }
        }
        
        function sendTestMessage() {
            if (socket && socket.connected) {
                const testData = {
                    busId: 'BUS001',
                    latitude: 37.7749 + (Math.random() - 0.5) * 0.01,
                    longitude: -122.4194 + (Math.random() - 0.5) * 0.01,
                    timestamp: new Date().toISOString()
                };
                
                socket.emit('bus:location:update', testData);
                logMessage('📤 Sent test location update: ' + JSON.stringify(testData));
            } else {
                logMessage('⚠️ WebSocket not connected');
            }
        }
        
        function logMessage(message) {
            const logArea = document.getElementById('wsLog');
            const timestamp = new Date().toLocaleTimeString();
            logArea.textContent += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        // Clear log on page load
        document.getElementById('wsLog').textContent = '[System] WebSocket log initialized\n';
    </script>
</body>
</html>
